import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { DomainPool } from '../../../../../../common/models/domain.model';
import { toDomainPool } from '../../../../../domains-management/domains-pool/domains-pool.service';
import { API_ENDPOINT } from '../../../../../../app.constants';

function getUrl(path: string, id?: string): string {
  return `${API_ENDPOINT}/entities/${path}/domain-pools/${id ? `${id}/` : ''}static`;
}

@Injectable()
export class EntityDomainPoolService {
  private readonly cachedItem: Record<string, DomainPool> = {};

  constructor(private readonly http: HttpClient,
              private readonly notifications: SwuiNotificationsService) {
  }

  get(path: string, force = false) {
    if (!force) {
      if (this.cachedItem[path]) {
        return of(this.cachedItem[path]);
      }
    }
    return this.http.get(getUrl(path), { params: { inherited: true } }).pipe(
      catchError(() => {
        return of(undefined);
      }),
      map(toDomainPool('static')),
      tap((data) => {
        this.cachedItem[path] = data;
      }),
    );
  }

  set(id: string, path: string) {
    return this.http.put(getUrl(path, id), {}).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
      switchMap(() => this.get(path, true)),
    );
  }

  remove(path: string) {
    return this.http.delete(getUrl(path)).pipe(
      tap(() => {
        delete this.cachedItem[path];
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }
}
