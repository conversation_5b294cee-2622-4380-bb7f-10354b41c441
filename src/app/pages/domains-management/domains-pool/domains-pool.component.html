<div class="mb-16">
  <button mat-raised-button color="primary" (click)="openAddPoolDialog()">
    <mat-icon>add</mat-icon>
    {{ 'DOMAINS.addPool' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="openAddPoolDialog()">
    <mat-icon>add</mat-icon>
    {{ 'DOMAINS.addDynamicPool' | translate }}
  </button>
</div>

<lib-swui-grid
  [pageSize]="pageSize"
  [schema]="schema"
  [data]="data"
  [columnsManagement]="false"
  [disableRefreshAction]="true"
  [rowActions]="rowActions">
  <button mat-icon-button [matTooltip]="'Refresh'" (click)="refreshGrid()">
    <mat-icon>refresh</mat-icon>
  </button>
</lib-swui-grid>
