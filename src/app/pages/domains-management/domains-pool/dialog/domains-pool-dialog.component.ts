import { ChangeDetectionStrategy, Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Domain, DomainPool, DomainType } from '../../../../common/models/domain.model';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DomainsManagementService } from '../../domains-management.service';

export interface DomainsPoolDialogData {
  pool?: DomainPool;
  poolType?: DomainType;
}

interface DomainPoolItem {
  code: string;
  displayName: string;
  type: 'lobby' | 'game';
  selected: boolean;
  enabled: boolean;
  active: boolean;
}

const compareRow = (row: DomainPoolItem) => (item: DomainPoolItem): boolean => item.type === row.type && item.code === row.code;

@Component({
  selector: 'domains-pool-dialog',
  templateUrl: './domains-pool-dialog.component.html',
  styleUrls: ['./domains-pool-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DomainsPoolDialogComponent implements OnInit, OnDestroy {
  dataSource: MatTableDataSource<DomainPoolItem>;
  displayedColumns: string[] = ['code', 'name', 'active', 'type'];
  submitted = false;
  poolType: DomainType = 'static';

  domains: Domain[] = [];
  staticDomains: Domain[] = [];
  dynamicDomains: Domain[] = [];

  private readonly form: FormGroup;
  private readonly items: DomainPoolItem[] = [];
  private readonly pool?: DomainPool;
  private _destroyed$ = new Subject();

  constructor(
    @Inject(MAT_DIALOG_DATA) {pool, poolType}: DomainsPoolDialogData,
    fb: FormBuilder,
    private readonly dialogRef: MatDialogRef<DomainsPoolDialogComponent>,
    private domainsManagementService: DomainsManagementService
  ) {
    this.poolType = poolType || 'static';
    this.pool = pool;

    this.form = fb.group({
      name: [pool && pool.name ? pool.name : '', Validators.required],
      type: [pool && pool.type ? pool.type : poolType, Validators.required],
    });
  }

  ngOnInit(): void {
    this.loadDomains();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  get selectedItems(): DomainPoolItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  get typeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  rowSelectedChanged(changeEvent: MatCheckboxChange, row: DomainPoolItem) {
    if (!changeEvent.checked) {
      const compareFn = compareRow(row);
      const record = this.dataSource.data.find(compareFn);
      if (record) {
        record.active = false;
      }
    }
  }

  rowActiveChanged(changeEvent: MatCheckboxChange, row: DomainPoolItem) {
    if (changeEvent.checked) {
      const compareFn = compareRow(row);
      const record = this.dataSource.data.find(compareFn);
      if (record) {
        record.selected = true;
      }
    }
  }

  submit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      const result: any = {
        name: this.nameControl.value,
        type: this.typeControl.value,
        domains: this.selectedItems.filter(({ type }) => type === 'game').map(item => ({
          id: item.code,
          isActive: item.active
        }))
      };
      this.dialogRef.close(result);
    }
  }

  private loadDomains() {
    // Load both static and dynamic domains
    this.domainsManagementService.getList('static').pipe(takeUntil(this._destroyed$)).subscribe(domains => {
      this.staticDomains = domains;
      this.updateAllDomains();
    });

    this.domainsManagementService.getList('dynamic').pipe(takeUntil(this._destroyed$)).subscribe(domains => {
      this.dynamicDomains = domains;
      this.updateAllDomains();
    });
  }

  private updateAllDomains() {
    this.domains = [...this.staticDomains, ...this.dynamicDomains];
    this.populateItems();
  }

  private populateItems() {
    this.items.length = 0; // Clear existing items
    for (const domain of this.domains) {
      const record = this.pool && this.pool.domains.find(({id}) => domain.id === id);
      this.items.push({
        code: domain.id || '',
        type: 'game',
        displayName: domain.domain || '',
        selected: Boolean(record),
        active: Boolean(record) && Boolean(record.isActive),
        enabled: true
      });
    }
    this.dataSource = new MatTableDataSource(this.items);
  }
}
