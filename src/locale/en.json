{"ALL": {"IN_PROGRESS": "In progress...", "REFRESH_SESSION": "Your session will expire in {{timeToExpire}}. Do you want to stay signed on?", "SERVICE_IS_UNAVAILABLE": "Service is unavailable", "SERVICE_IS_UNAVAILABLE_DESC": "Please contact technical support", "MULTIPLIER": "Multiplier", "all": "All", "DEFAULT_SCHEMA_SECTIONS": {"basic": "Basic", "operatorAgent": "Operator/Agent", "balance": "Balance", "financial": "Financial", "other": "Other", "dateTime": "Date/Time"}, "confirm": "Apply", "save": "Save", "decline": "Cancel", "copyToClipboard": "Copy to clipboard", "noItemsFound": "No items found", "itemsFound": "{{value}} items found", "noData": "No data", "urlFormat": "URL format：http(s)://example.com", "reset": "Reset", "resetDefault": "Reset to default", "delete": "Delete", "search": "Search", "title": "Title", "description": "Description", "key": "Key", "reseller": "Reseller", "operator": "Operator", "merchant": "Merchant", "liveStudio": "Live Studio", "skywind": "Skywind", "inherited": "Inherited", "active": "Active", "inactive": "Inactive", "inactive_keep": "Inactive (Keep active sessions alive)", "inactive_kill": "Inactive (Kill active sessions)", "test": "Test", "hidden": "Hidden", "pleaseSelect": "Please select...", "settings": "Settings", "code": "Code", "available": "{{numberOfItems}} available", "found": "{{numberOfItems}} found", "selected": "{{numberOfItems}} selected", "selectAll": "Select All", "resellerOperator": "Reseller / Operator", "general": "General", "games": "Games", "more": "more", "dontShowAgain": "Do not show again"}, "CHART": {"X-AXIS-TITLE-DEFAULT": "Values"}, "DASHBOARD": {"AVERAGE_PAYMENTS": "Average Payments", "GGR": "GGR", "GGR (EUR)": "GGR (EUR)", "PAYMENTS": "Payments", "TITLE": "Summary report", "TOP_GAMES": "Top Games", "Total Bet (EUR)": "Total Bet (EUR)", "Total Win (EUR)": "Total Win (EUR)"}, "DIALOG": {"decline": "Decline", "no": "No", "ok": "OK", "yes": "Yes", "back": "Back", "next": "Next", "previous": "Prev", "close": "Close", "cancel": "Cancel", "save": "Save", "clone": "<PERSON><PERSON>", "saveChanges": "Save Changes", "сomplete": "Complete", "activate": "Activate", "deactivate": "Deactivate", "active": "Active", "inactive": "Inactive", "saveAndClone": "Save & Clone", "finish": "Finish"}, "FILTER": {"FINISHED": {"finished": "Finished", "unfinished": "Unfinished"}, "newFilter": "New filter name", "noFilterData": "No filter data", "noSavedFilters": "No saved filters", "rangeFrom": "From", "rangeTo": "To", "reset": "Reset", "search": "Search", "currencyrangePlaceholder": "Please select currency...", "resellerOperatorRequired": "Reseller / Operator (required)", "resellerOperator": "Reseller / Operator", "masterAll": "MASTER - ALL"}, "FORGOTPASSWORD": {"title": "Forgot password", "desc": "Enter your username or email address and we'll send you a reminder of your details along with instructions on how to reset your password.", "username": "Username or email", "submit": "Send request", "success-notification": "The reset link has been sent to your email. Please follow the link to proceed the password reset.", "to-login": "To login page", "notificationReset": "Password was successfully reset"}, "MENU_SECTIONS": {"dashboard": "Dashboard", "businessManagement": "Business Management", "businessManagement_structure": "Business Structure", "businessManagement_transfers": "Transfers", "businessManagement_agents": "Agents", "businessManagement_entities": "Entities", "businessManagement_entitySetup": "<PERSON><PERSON><PERSON> Setup", "users": "Users", "usersList": "Users List", "userRoles": "Roles", "usersActivityLog": "Activity Log", "customers": "Players", "payments": "Payments", "paymentsDeposits": "Deposits", "paymentsWithdrawals": "<PERSON><PERSON><PERSON><PERSON>", "paymentsTransfers": "Transfers", "paymentsTransfpaymentGroupsers": "Payment Groups", "marketing": "Marketing", "marketingPromotions": "Promotions", "marketingMaterials": "Marketing materials", "reports": "Reports", "reportsCurrency": "Currency report", "reportsFinancial": "Financial report", "reportsPlayers": "Players report", "reportsKPI": "KPI reports", "reportsRG": "Exclusions and Timeouts", "lobbies": "Lobbies", "lobbiesLayouts": "Lobby Layouts", "retail": "Retail", "retailTerminals": "Terminals", "games": "Games", "gamesHistory": "Game History", "externalGamesHistory": "External Game History", "gamesLobbies": "Lobbies", "gamesLobbiesManagement": "Lobby Management", "gamesManagement": "Games Availability", "gamesCategoriesManagement": "Games Categories", "support": "Support", "supportFAQ": "FAQ", "supportSupportTickets": "Support Tickets", "supportSystemNotifications": "System Notifications", "settings": "Settings", "settings_general": "General", "settings_domains": "Domains", "settings_grc": "GRC", "settings_jurisdictions": "Jurisdictions", "settings_labels": "Labels Management", "settings_gameserver": "Game server", "settings_proxy": "Proxy", "bulkActions": "Entity Bulk Actions", "financialReport": "Financial report", "integrations": "Integrations", "cashier": "Cashier", "gameManagement": "Games management", "liveLimits": "Live Limits", "bi-reports-switch": "BI reports switch", "id-transcode": "ID Transcode"}, "CASHIER": {"playerUsername": "Player username", "amount": "Amount", "createPlayer": "Create Player", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashout": "Cashout", "seePlayerInfo": "See player info", "balance": "Balance", "customerID": "Player ID", "firstName": "First Name", "lastName": "Last Name", "login": "<PERSON><PERSON>", "email": "E-mail", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "type": "Type", "profileUpdate": "Profile Update", "lastLogin": "Last Login", "registered": "Registered", "country": "Country/City", "lookingForPlayer": "Looking for a player", "playerNotFound": "Player not found"}, "CUSTOMERS": {"playerIsOnline": "Withdrawal cannot be processed for online players", "setActive": "Set active status", "setInactive": "Set inactive status", "defaultGameGroupLabel": "\"{{ defaultGameGroup }}\" game group will be used", "defaultLimitsLabel": "Default limits will be applied to this player (because of empty game group)", "NICKNAME_CHANGE": {"nickname": "Nickname"}, "GRID": {"contentProvider": "Content provider", "contentProvider_placeholder": "In progress...", "operator": "Operator", "operator_scope": "operator/agent", "agentDomain": "Agent", "agentDomain_scope": "operator/agent", "code": "Player ID", "login": "<PERSON><PERSON>", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "username_placeholder": "In progress...", "email": "E-mail", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_scope": "balance", "balance": "Balance", "balance_scope": "balance", "balances": "Balances", "balances_scope": "balance", "balanceRange": "Balance range", "balanceRange_placeholder": "In progress...", "balanceRange_scope": "balance", "hasDeposits": "Has deposits", "hasDeposits_placeholder": "In progress...", "hasDeposits_scope": "balance", "hasWithdrawals": "Has withdrawals", "hasWithdrawals_placeholder": "In progress...", "hasWithdrawals_scope": "balance", "provider": "PMT Group", "status": "Status", "isTest": "Type", "isTest_placeholder": "type", "createdAt": "Registration Date", "paymentDateHour": "Payment Date", "createdAt_scope": "date/time", "lastLogin": "Last Login", "lastLogin_scope": "date/time", "country": "Country", "country_scope": "other", "isOnline": "Online status", "isOnline_scope": "other", "registrationIP": "Registration IP", "registrationIP_scope": "other", "createPlayer": "Create Player", "resellerOperator": "Reseller / Operator", "gameGroup": "Game limits group"}, "MODAL": {"createPlayer": "Create Player", "btnClose": "Close", "btnApply": "Apply", "btnSave": "Save Changes", "create": "Create", "id": "Player ID", "email": "Email", "password": "Password", "name": "First Name", "lastName": "Last Name", "status": "Status", "active": "Active", "inactive": "Inactive", "country": "Country", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "promoFreebetTitle": "Apply Freebet promotion", "promoBonusCoinTitle": "Apply Bonus Coin promotion", "promotion": "Promotion", "code": "Code", "result": "Result"}, "LABEL": {"testAccount": "Test", "realAccount": "Real", "userOnline": "Online", "userOffline": "Offline", "userActive": "Active", "userInactive": "Inactive", "userSuspended": "Suspended", "unblocked": "Unblocked", "blocked": "Blocked"}, "DETAILS": {"type": "Type", "editPlayer": "Edit player {{player}}", "withdrawal_password": "<PERSON><PERSON><PERSON> Password", "id": "ID", "registered": "Registered", "profile_update": "Profile update", "account": "account", "save": "Save", "back": "Back", "blockedStatus": "Blocked status", "CUSTOMFIELDS": {"phone": "Phone", "postal_code": "Postal Code", "province": "Province", "city": "City", "address": "Address", "date_of_birth": "Date of birth"}, "BALANCE": {"transfer": "Transfer", "amount": "Amount", "close": "Close", "in": "in", "out": "out", "transferIn": "Transfer in", "transferOut": "Transfer out"}, "TABS": {"general": "General", "payments": "Payments", "promotions": "Promotions", "game_history": "Game History", "responsible_gaming": "Responsible Gaming"}, "VIEWSTATE": {"Normal": "Normal", "Expanded": "Expanded", "Full screen": "Full screen", "fullscreen": "Full screen", "newTab": "Open in a new tab"}}, "PAYMENTS": {"TABS": {"deposits": "Deposits", "withdrawals": "<PERSON><PERSON><PERSON><PERSON>"}}, "NOTIFICATIONS": {"status_inactive": "Status was successfully set to inactive", "status_active": "Status was successfully set to active", "type_real": "Type was successfully set to real", "type_test": "Type was successfully set to test", "not_set": "Not set", "transfer_success": "Successfully transferred!", "edit_success": "Player ID: {{ code }} was successfully edited", "unblock": "Player ID: {{ code }} was successfully unblocked", "block": "Player ID: {{ code }} was successfully blocked"}, "RG": {"TIMEOUT": {"title": "Time out", "text": "If player would like to take a break from Casino betting, you can easily restrict his access for a period of time. Simply select the product player would like to take a time out from, and choose a duration.", "notification_success": "Player's time out period is successfully changed", "CASINO": {"text": "Choose time out duration. Player won’t be able to play any Casino games as during the time out period, and the block can’t be removed once it’s set. Player may however still receive related special offers via from us.", "button_submit": "Save", "MODAL": {"confirmation_1": "Player will not be able to play Casino games for", "confirmation_2": "The restriction will be removed on", "confirmation_3": "Are you sure?"}, "NOTICE": {"notice_1": "Player time out period will end on"}, "LIMITS": {"Not set": "Not set", "1 day": "1 day", "2 days": "2 days", "1 week": "1 week", "3 weeks": "3 weeks", "6 weeks": "6 weeks"}}}, "SELFEXCLUSION": {"title": "Self-exclusion", "text": "Stay in control with our self-exclusion options, where you can block access to player's account for a set time period. Player will not be able to play or deposit during this period, but player still be able to withdraw any available funds by contacting us.", "notification_success": "Player's self-exclusion period is successfully changed", "CASINO": {"text": "Choose self-exclusion duration. Please note that we will not notify player when the self-exclusion period is over.", "button_submit": "Save", "LIMITS": {"Not set": "Not set", "6 months": "6 months", "1 year": "1 year", "3 years": "3 years", "5 years": "5 years"}, "MODAL": {"confirmation_1": "Player will not be able to play Casino games for", "confirmation_2": "You will not be able to remove this restriction. Are you sure?"}, "NOTICE": {"notice_1": "Player's self-exclusion period will end on"}}}, "REALITYCHECK": {"title": "Game time reminder", "text": "Game time reminder helps player to keep track of his gambling, with alerts that let player know how long he has been active. You can choose whether or not to receive Game time reminders, and also select how often they appear", "notification_success": "Game time reminder settings have been saved", "CASINO": {"text": "Show game time reminder every", "button_submit": "Save game time reminder", "REMINDS": {"off": "off", "20 min": "20 min", "40 min": "40 min", "1 hour": "1 hour", "2 hours": "2 hours", "3 hours": "3 hours"}, "MODAL": {"confirmation_1": "Reality check will be set to", "confirmation_2": "Are you sure?"}}}, "LOSSLIMIT": {"title": "Loss limit", "text": "Manage player's gambling by setting a loss limit on Casino and Sports betting", "notification_success": "Player's loss limit is successfully changed", "label": "amount:", "LIST": {"item_1": "Daily limit refreshes at 00:00 CET every day.", "item_2": "Weekly limit refreshes at 00:00 CET every Monday.", "item_3": "Monthly limit refreshes at 00:00 CET on the 1st of every month."}, "CASINO": {"text": "Set loss limit for Casino bets:", "button_submit": "Save", "LIMITS": {"no": "no", "daily": "daily", "weekly": "weekly", "monthly": "monthly"}, "MODAL": {"confirmation_1": "Please note that once you set this limit, you cannot immediately remove it. Changing to a less restrictive value requires a cooling-off period before taking effect. Are you sure you want to set a", "confirmation_2": "Changing or removing a loss limit requires a cooling-off period. Player limit will be changed in {{ time }}", "confirmation_3": "Are you sure you want to make this change?"}, "NOTICE": {"notice_1": "Player's loss limit is set to", "notice_2": "of", "notice_3": "to", "notice_4": "limit", "notice_5": "Player's loss limit is changed from", "notice_6": "Limit will be changed"}}}, "DEPOSITLIMIT": {"title": "Deposit limit", "text": "Deposit limit decides how much player can deposit to his account during the selected period.", "notification_success": "Player's deposit limit is successfully changed", "label": "amount:", "LIST": {"item_1": "Daily limit refreshes at 00:00 CET every day.", "item_2": "Weekly limit refreshes at 00:00 CET every Monday.", "item_3": "Monthly limit refreshes at 00:00 CET on the 1st of every month."}, "CASINO": {"text": "Set deposit limit for Casino bets:", "button_submit": "Save", "LIMITS": {"no": "no", "daily": "daily", "weekly": "weekly", "monthly": "monthly"}, "NOTICE": {"notice_1": "Player's deposit limit is set to", "notice_2": "of", "notice_3": "to", "notice_4": "limit", "notice_5": "Player's deposit limit is changed from", "notice_6": "Limit will be changed"}, "MODAL": {"confirmation_1": "Please note that once you set this limit, you cannot immediately remove it. Changing to a less restrictive value requires a cooling-off period before taking effect. Are you sure you want to set a", "confirmation_2": "Changing or removing a deposit limit requires a cooling-off period. Player's limit will be changed in 24 hours", "confirmation_3": "Are you sure you want to make this change?"}}}, "NOTICE": {"BUTTON": {"Change limit": "Change limit", "Cancel pending change": "Cancel pending change"}}, "MODAL": {"confirm": "Yes", "decline": "No"}, "pending": "7 days"}, "PROMO": {"promotion": "Promotion", "bonusCoins": "Bonus Coins", "freeBets": "Free Bets", "rebates": "Rebates", "virtualMoney": "Virtual Money"}, "CSV": {"brandTitle": "Operator", "code": "Player ID", "firstName": "First Name", "lastName": "Last Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "type": "Type", "createdAt": "Registration Date (GMT {{zone}})", "updatedAt": "Last Login (GMT {{zone}})", "country": "Country", "balance": "Balance"}}, "GAMEHISTORY": {"freeSpinsWon": "{{number}} free spins won", "freeSpinsRemaining": "{{number}} free spins remaining", "TYPE": {"historyType": "History type", "gameHistoryLog": "Game History Log", "unfinishedBroken": "Unfinished and Broken"}, "GRID": {"account_type": "Account type", "agentDomain": "Agent", "agentDomain_scope": "operator/agent", "balanceAfter": "Balance After", "balanceBefore": "Balance Before", "bet": "Bet Amount", "bet_scope": "financial", "brandId": "Operator", "brandId_scope": "operator/agent", "contentProvider": "Provider", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_placeholder": "USD, EUR...", "currency_scope": "financial", "customerCode": "Player code", "device": "<PERSON><PERSON>", "isFinished": "Finished", "unfinished": "Unfinished", "broken": "Broken", "isTest": "Test", "gameCode": "Game Code", "gameNameLabel": "Game Name", "ggrPerc": "GGR %", "operator": "Operator", "outcome": "Outcome", "playerCode": "Player ID", "playerCodeRequired": "Player ID (required)", "revenue": "Revenue", "debit": "Debit", "credit": "Credit", "revenue_scope": "financial", "roundId": "Round ID", "eventId": "Event ID", "extRoundId": "PT Round ID", "status": "Status", "firstTs": "First Date/Time", "ts": "Last Date/Time", "win": "Win Amount", "win_scope": "financial", "labelWin": "Win", "labelLose": "Lose", "labelTie": "Tie", "labelVoid": "Void", "view": "View", "internalRoundId": "Internal Round ID", "insertedAt": "Inserted at", "testAccount": "Test", "realAccount": "Real", "resellerOperator": "Reseller / Operator (required)", "transactionId": "Transaction ID", "gameProvider": "Game Provider", "accountType": "Account type", "onlyTest": "Only Test", "onlyReal": "Only Real", "onlyFinished": "Only Finished", "onlyUnfinished": "Only Unfinished", "onlyMobile": "Only Mobile", "onlyWeb": "Only Web", "dateTime": "Date/Time", "test": "Test", "real": "Real", "brandIdTitle": "Brand ID", "totalJpWin": "<PERSON>", "totalJpContribution": "JP Contribution", "requireLogout": "Require logout", "brokenIntegration": "Broken integration", "finalizing": "Finalizing", "aamsSessionCode": "AAMS Code", "ropCode": "Session Code", "participationStartDate": "ROP Date", "tableName": "Table Name", "recoveryType__ne": "Recovery type is not", "recoveryType": "Recovery type", "force-finish": "Force-finish", "finalize": "Finalize", "null": "<PERSON><PERSON>"}, "GAME": {"freespin": "Free spin", "freebet": "Free bet", "respin": "Respin", "round": "Round", "spin": "Spin", "spinId": "Spin ID", "operationId": "Operation ID", "date": "Date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "freeSpinsRemaining": "{{value}} free spins remaining", "balanceBefore": "Balance before", "balanceAfter": "Balance after", "selectSymbol": "Select symbol #{{value}}", "rewardsSymbol": "Symbol", "rewardsExplode": "Explode", "Bonus": "Bonus #{{row.spinNumber}}", "bonus": "Bonus", "bonusResult": "Bonus result", "bet": "Bet", "betAmount": "Bet Amount", "betCoinLines": "bet/coin/lines", "betLines": "bet/lines", "collectedOnReel": "Collected on reel", "win": "Win", "stake": "Stake", "gameVersion": "Game version", "tableName": "Table name", "jackpot": "Jackpot", "jackpotId": "Jackpot ID", "jackpotShot": "Jackpot Shot", "jackpotReel": "Jack<PERSON>", "jackpotWheel": "Jackpot Wheel", "jackpotStatus": "Jackpot status", "jackpotInProgress": "Jackpot in progress", "jackpotReelType": "Jackpot reel type", "jackpotFinalScene": "Jackpot final scene", "gameState": "Game state", "extraWin": "Extra win", "extraMultiplier": "Extra multiplier", "extraSum": "Extra sum", "pool": "Pool", "amount": "Amount", "bonusSectionID": "Bonus selection ID", "freeSpinsCount": "Free spins count", "freeSpinsCountOf": "Free spins: number (total)", "freeSpinsWithStackedSymbol": "Free spins with stacked symbol", "freeSpins": "Free spins", "multiplier": "Multiplier", "transactionID": "Transaction ID", "type": "Type", "endOfRound": "End of Round", "gameId": "Game ID", "isPayment": "Is Payment", "gameSymbolID": "Symbol ID: {{code}}", "rewardsHeadReward": "<PERSON><PERSON>", "rewardsHeadMultiplier": "Multiplier", "rewardsHeadWin": "Win", "rewardsWay": "Way", "rewardsLine": "Line", "rewardsScatter": "<PERSON><PERSON><PERSON>", "rewardsExtraScatter": "Extra scatter", "rewardsInstantCash": "Instant Cash", "maxWinReached": "Max win reached", "coinRedeems": "Coin redeems", "bonusCounsRedeems": "Bonus Сoins redeem", "capped": "capped", "stand": "Stand", "maxWinReachedAmount": "Max win reached amount", "copyJsonToClipboard": "<PERSON><PERSON> JSON to clipboard", "downloadDetailedCSV": "Download detailed CSV", "notificationCopy": "Selected spin was copied to clipboard!", "notificationRoundCopy": "Selected round was copied to clipboard!", "notificationCopyFailed": "Copy to clipboard failed!", "notificationHistoryLoaded": "Spin history was loaded", "tournamentWin": "Tournament Win", "tournamentName": "Tournament Name", "tournamentId": "Tournament ID", "dateTime": "Date/Time", "paymentType": "Payment type", "sharedJackpotPrize": "Shared Jackpot prize", "finalization": "Finalization", "sharedPrizePayment": "Shared Prize payment", "paymentTime": "Payment time", "prizeDrop": "Prize Drop", "prizeWin": "Prize Win", "featureName": "Feature Name", "winAmount": "Win Amount", "featureId": "Feature ID", "prize": "Prize", "operationType": "Operation type", "disconnect": "Disconnect", "CARD": {"rank": "rank", "split": "Split", "cardDealer": "Dealer", "cardHand": "Hand", "insurance": "Insurance", "total": "Total", "play": "Play", "ante": "Ante", "pairBonus": "Pair bonus", "anteBonusWin": "Ante bonus", "bet": "Bet", "win": "Win", "stake": "Stake", "playWin": "Play win", "stakeWin": "Ante win", "doubleBet": "Double bet", "doubleWin": "Double win", "insuranceBet": "Insurance bet", "insuranceWin": "Insurance win", "pairWin": "Pair win", "pairBet": "Pair bet"}, "freeSpinsStacked": "Free spins stacked", "freeSpinsSticky": "Free spins sticky", "freeSpinsExpanding": "Free spins expanding", "sw_sq": {"bonus": "Bonus", "gem": "Gem", "result": "Result", "selectedGem": "Selected gem"}, "sw_tcb": {"request_fold": "Fold", "HAND_COMBINATION_FLUSH": "FLUSH", "HAND_COMBINATION_HIGH_CARD": " HIGH", "HAND_COMBINATION_NOT_QUALIFY": "DEALER DOES NOT QUALIFY", "HAND_COMBINATION_PAIR": "PAIR", "HAND_COMBINATION_PAIR_10": "PAIR OF TENS", "HAND_COMBINATION_PAIR_2": "PAIR OF TWOS", "HAND_COMBINATION_PAIR_3": "PAIR OF THREES", "HAND_COMBINATION_PAIR_4": "PAIR OF FOURS", "HAND_COMBINATION_PAIR_5": "PAIR OF FIVES", "HAND_COMBINATION_PAIR_6": "PAIR OF SIXES", "HAND_COMBINATION_PAIR_7": "PAIR OF SEVENS", "HAND_COMBINATION_PAIR_8": "PAIR OF EIGHTS", "HAND_COMBINATION_PAIR_9": "PAIR OF NINES", "HAND_COMBINATION_PAIR_A": "PAIR OF ACES", "HAND_COMBINATION_PAIR_J": "PAIR OF JACKS", "HAND_COMBINATION_PAIR_K": "PAIR OF KINGS", "HAND_COMBINATION_PAIR_Q": "PAIR OF QUEENS", "HAND_COMBINATION_PRIAL": "PRIAL", "HAND_COMBINATION_RANK_10": "Hight card 10", "HAND_COMBINATION_RANK_2": "Hight card 2", "HAND_COMBINATION_RANK_3": "Hight card 3", "HAND_COMBINATION_RANK_4": "Hight card 4", "HAND_COMBINATION_RANK_5": "Hight card 5", "HAND_COMBINATION_RANK_6": "Hight card 6", "HAND_COMBINATION_RANK_7": "Hight card 7", "HAND_COMBINATION_RANK_8": "Hight card 8", "HAND_COMBINATION_RANK_9": "Hight card 9", "HAND_COMBINATION_RANK_A": "Hight card ACE", "HAND_COMBINATION_RANK_J": "Hight card JACK", "HAND_COMBINATION_RANK_K": "Hight card KING", "HAND_COMBINATION_RANK_Q": "Hight card QUEEN", "HAND_COMBINATION_RUN": "RUN", "HAND_COMBINATION_RUNNING_FLUSH": "RUNNING FLUSH", "HAND_COMBINATION_RUNNING_FLUSH_A23": "RUNNING FLUSH A-2-3", "HAND_COMBINATION_RUN_A23": "RUN A-2-3"}, "sw_er": {"positions": "Positions", "stopPosition": "Stop position", "payout": "Payout", "StraightPayout": "Straight", "SplitPayout": "Split", "CornerPayout": "Corner", "LinePayout": "Line", "ColumnDozenPayout": "Column dozen", "ColorPayoutDefinition": "Color", "HighLowPayout": "High low", "OddPayoutDefinition": "Odd", "EvenPayoutDefinition": "Even", "colorRED": "Red", "colorBLACK": "Black"}, "sw_bjc": {"request_stand": "Stand"}, "POOL": {"mega": "Mega", "big": "Grand", "medium": "Major", "small": "Minor", "major": "Major", "minor": "Minor", "grand": "Grand", "mini": "Mini", "A": "GRAND", "B": "MAJOR", "C": "MINOR", "D": "MINI", "super": "Super", "main": "<PERSON><PERSON> Shi Jackpot", "fruit-vegas": "Viva Fruit Vegas", "monkey-pays": "Monkey Pays", "x2": "X2", "x3": "X3", "x5": "X5", "x10": "X10", "X2": "X2", "X3": "X3", "X5": "X5", "X10": "X10", "must_win_size": "Must Win Jackpot", "level1": "Major", "level2": "Major", "level3": "Major", "level4": "Major", "level5": "Major", "level6": "Major", "level7": "Major", "amount-1": "Amount Jackpot", "amount-2": "Amount Jackpot", "amount-3": "Amount Jackpot", "hourly-1": "Hourly Jackpot", "grand-1": "No Limit Jackpot", "daily-1": "Daily Jackpot", "sw_wfww": {"main": "<PERSON> Fu Wa Wa Jackpot"}, "sw_omqjp": {"A": "OLD MASTER Q", "B": "BIG POTATO", "C": "MR. CHIN"}, "sw_roriyang": {"A": "GRAND", "B": "MAJOR", "C": "MINI"}, "sw_lfs": {"maya-jp": "Liu Fu Shou Jackpot"}, "sw_dosc7s": {"two-powerful-dragons": "<PERSON> Scatter 7's"}, "sw_ar": {"mini": "Mini", "minor": "Minor", "sw-fire-reel-major": "Major", "sw-reel-mega": "Mega"}, "sw_azreeu": {"mini": "Mini", "minor": "Minor", "sw-aztec-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_azresl": {"mini": "Mini", "minor": "Minor", "sw-aztec-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_fr": {"mini": "Mini", "minor": "Minor", "sw-fire-reel-major": "Major", "sw-reel-mega": "Mega"}, "sw_frreeu": {"mini": "Mini", "minor": "Minor", "sw-aztec-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_frresl": {"mini": "Mini", "minor": "Minor", "sw-aztec-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_glreeu": {"mini": "Mini", "minor": "Minor", "sw-gladiator-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_glre_slp": {"mini": "Mini", "minor": "Minor", "sw-gladiator-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_mr": {"mini": "Mini", "minor": "Minor", "sw-metal-reel-major": "Major", "sw-reel-mega": "Mega"}, "sw_noreeu": {"mini": "Mini", "minor": "Minor", "sw-river-boat-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_noresl": {"mini": "Mini", "minor": "Minor", "sw-river-boat-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_poreeu": {"mini": "Mini", "minor": "Minor", "sw-gladiator-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_poresl": {"mini": "Mini", "minor": "Minor", "sw-gladiator-reel-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_rr": {"mini": "Mini", "minor": "Minor", "sw-water-reel-major": "Major", "sw-reel-mega": "Mega"}, "sw_rireeu": {"mini": "Mini", "minor": "Minor", "sw-river-boat-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_riresl": {"mini": "Mini", "minor": "Minor", "sw-river-boat-major-eu": "Major", "sw-reel-mega-eu": "Mega"}, "sw_wrl": {"mini": "Mini", "minor": "Minor", "sw-water-reel-major": "Major", "sw-reel-mega": "Mega"}, "sw_desheurb": {"minor": "Minor", "major": "Major", "grand-genie-shot-eu": "Grand", "super-shot-eu": "Super"}, "sw_ges": {"minor": "Minor", "major": "Major", "grand-genie-shot": "Grand", "super-shot": "Super"}, "sw_ges_eu": {"minor": "Minor", "major": "Major", "grand-genie-shot-eu": "Grand", "super-shot-eu": "Super"}, "sw_gs": {"minor": "Minor", "major": "Major", "grand-gold-shot": "Grand", "super-shot": "Super"}, "sw_gs_eu": {"minor": "Minor", "major": "Major", "grand-gold-shot-eu": "Grand", "super-shot-eu": "Super"}, "sw_hosheu": {"minor": "Minor", "major": "Major", "grand-pot-shot-eu": "Grand", "super-shot-eu": "Super"}, "sw_mdls": {"minor": "Minor", "major": "Major", "grand-gold-shot": "Grand", "super-shot": "Super"}, "sw_ps": {"minor": "Minor", "major": "Major", "grand-pot-shot": "Grand", "super-shot": "Super"}, "sw_ps_eu": {"minor": "Minor", "major": "Major", "grand-pot-shot-eu": "Grand", "super-shot-eu": "Super"}, "sw_tisheu": {"minor": "Minor", "major": "Major", "grand-gold-shot-eu": "Grand", "super-shot-eu": "Super"}, "sw_ws": {"minor": "Minor", "major": "Major", "grand-pot-shot": "Grand", "super-shot": "Super"}, "sw_suli": {"main": "Jackpot"}, "sw_suli_chf": {"main": "Jackpot"}, "sw_sulisped": {"main": "Jackpot"}, "sw_suel": {"main": "Jackpot"}, "sw_ges2": {"grand-genie-shot": "Grand"}, "sw_ff": {"main": "Fire Festival Jackpot"}, "sw_doab": {"downton_abbey_crawley": "<PERSON><PERSON><PERSON>", "downton_abbey_grantham": "<PERSON><PERSON>", "downton_abbey_village": "Village"}, "sw_doab_slp": {"downton_abbey_crawley_slp": "<PERSON><PERSON><PERSON>", "downton_abbey_grantham_slp": "<PERSON><PERSON>", "downton_abbey_village_slp": "Village"}, "sw_doab_slp_es": {"downton_abbey_crawley_slp": "<PERSON><PERSON><PERSON>", "downton_abbey_grantham_slp": "<PERSON><PERSON>", "downton_abbey_village_slp": "Village"}, "sw_kkit": {"karate_kid_italy": "Karate Kid <PERSON>"}, "sw_thmase": {"magnificent_seven": "The Magnificent Jackpot"}, "sw_thmase_slp": {"magnificent_seven": "The Magnificent Jackpot"}, "sw_kk": {"karate-kid": "<PERSON><PERSON>"}, "sw_kaki_slp": {"karate-kid": "<PERSON><PERSON>"}, "sw_2pd": {"two-powerful-dragons": "Two Powerful Dragons"}, "sw_gemerenigael": {"genie_mega_reels": "Mega"}, "sw_bloo": {"blood_sport_major": "Major <PERSON><PERSON>", "blood_sport_grand": "Grand Jackpot"}, "sw_bl_slp": {"blood_sport_major": "Major <PERSON><PERSON>", "blood_sport_grand": "Grand Jackpot"}, "sw_bl_slp_es": {"blood_sport_major": "Major <PERSON><PERSON>", "blood_sport_grand": "Grand Jackpot"}, "sw_ijp": {"maya-jp": "Inca Jackpot"}, "sw_taoftwdrjaed": {"gold": "Red", "standard": "Blue"}, "sw_myjp": {"maya-jp": "Maya Jackpot"}, "sw_reki": {"jackpot-1_0": "Jackpot 5 crowns", "jackpot-1_1": "Jackpot 5 crowns", "jackpot-1_2": "Jackpot 5 crowns", "jackpot-1_3": "Jackpot 5 crowns", "jackpot-1_4": "Jackpot 5 crowns", "jackpot-2_0": "Jackpot 6 crowns", "jackpot-2_1": "Jackpot 6 crowns", "jackpot-2_2": "Jackpot 6 crowns", "jackpot-2_3": "Jackpot 6 crowns", "jackpot-2_4": "Jackpot 6 crowns", "jackpot-3_0": "Jackpot 7 crowns", "jackpot-3_1": "Jackpot 7 crowns", "jackpot-3_2": "Jackpot 7 crowns", "jackpot-3_3": "Jackpot 7 crowns", "jackpot-3_4": "Jackpot 7 crowns", "jackpot-4_0": "Jackpot 8 crowns", "jackpot-4_1": "Jackpot 8 crowns", "jackpot-4_2": "Jackpot 8 crowns", "jackpot-4_3": "Jackpot 8 crowns", "jackpot-4_4": "Jackpot 8 crowns", "jackpot-5_0": "Jackpot 9+ crowns", "jackpot-5_1": "Jackpot 9+ crowns", "jackpot-5_2": "Jackpot 9+ crowns", "jackpot-5_3": "Jackpot 9+ crowns", "jackpot-5_4": "Jackpot 9+ crowns"}, "sw_rori_es": {"A": "GRAND", "B": "MAJOR", "C": "MINI"}, "sw_gofofe": {"sw-gold-fortune-festival-jp": "Jackpot"}}, "SRT": {"challengeWinPool": "Challenge Win", "tournamentWinPool": {"pool0": "Tournament Win - Bet Range 1", "pool1": "Tournament Win - Bet Range 2", "pool2": "Tournament Win - Bet Range 3", "pool3": "Tournament Win - Bet Range 4"}, "tournament": "Tournament", "challenge": "Challenge", "playerCoins": "Player coins", "coins": "Coins", "collectedBadges": "Collected badges", "redeemAmount": "Redeem amount", "vipLevel": "VIP level", "points": "Points", "winAmount": "Win Amount", "xpAmount": "XP amount", "xp": "XP"}}, "EXTERNAL": {"messageForceFinish": "You are about to force-finish the round. The round will be closed as it is and all remaining bonus features will not be played out. Additionally, the bet and wins will not be reverted. Are you sure?"}, "INTERNAL": {"ignoreMerchantParams": "You are going to force finish round ignoring the supportForceFinishAndRevert flag of the merchant and close round in SW wallet. Are you sure?", "messageForceFinishBroken": "You are going to force finish broken round. Please note: it will not revert bets and wins; it may caused payments loss. This action cannot be cancelled once finished. Are you sure?", "messageForceFinish": "You are going to force finish round. Please note that it will not revert bets and wins. This action cannot be cancelled once finished. Are you sure?", "messageRevertBroken": "You are going to close this broken round and revert bets and wins. Please note: it may caused payments loss. Do you really want to revert (this action cannot be cancelled once finished)?", "messageRevert": "You are going to close this round and revert bets and wins. This action cannot be cancelled once finished. Do you really want to revert?", "messageRetry": "Do you really want to retry pending?", "messageTransferOut": "Do you really want to transfer out for round?", "messageFinalize": "Do you really want to finalize the round?", "messageManualFinalize": "Do you really want to finalize the round manually?", "yesProceed": "Yes, Proceed", "yesRevert": "Yes, <PERSON>ert", "yesRetry": "Yes, Retry", "notify": "Please specify at least one of these parameters to proceed with search: Player ID, Round ID, Only Unfinished", "notifyUnfinished": "Please specify 'Player ID' to proceed with search", "notifyNote": "Note: 'Reseller / Operator' field is required", "forceFinish": "Force finish", "revert": "<PERSON><PERSON>", "retryPending": "Retry pending", "requireTransferOut": "Transfer out", "finalize": "Finalize", "manualFinalize": "Manually finalize", "NOTIFICATIONS": {"forceFinished": "Round has been finished successfully", "roundFinished": "Round has been finished successfully, all events are naturally completed", "roundClosed": "Round has been closed successfully", "roundFinalized": "Round has been finalized successfully", "betsReverted": "Bets and wins have been reverted", "noForceFinish": "No need to force finish", "noRevert": "No need to revert", "noRetry": "No need to retry pending", "retrySuccess": "Retry pending is successful", "note": "Please note: round status in table will be updated in 2 minutes"}}, "CSV": {"playerCode": "Player ID", "roundId": "Round ID", "gameName": "Game Name", "gameCode": "Game Code", "finished": "Finished", "device": "<PERSON><PERSON>", "type": "Account type", "firstTs": "First Date/Time (GMT {{zone}})", "ts": "Last Date/Time (GMT {{zone}})", "currency": "<PERSON><PERSON><PERSON><PERSON>", "bet": "Bet Amount", "win": "Win Amount", "revenue": "Revenue", "outcome": "Outcome", "ggr": "GGR %", "totalJpWin": "Jp Win", "actions": "actions", "extTrxId": "Transaction ID", "gameProviderCode": "Game Provider", "insertedAt": "Date/Time (GMT {{zone}})", "balanceBefore": "Balance Before", "balanceAfter": "Balance After", "status": "Status", "credit": "Credit", "debit": "Debit"}}, "REPORT_PLAYERS": {"note": "Please, select an Operator to proceed with search", "filterNote": "Please specify Date within one month period. You can see only history values for the past 3 months", "GRID": {"paymentDate": "Date", "playerCode": "Player ID", "gameCode": "Game Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_placeholder": "USD, EUR...", "playedGames": "Played Games", "totalBets": "Total Stakes", "totalFreebetWins": "Free Bets Total Win", "totalJpWins": "Jackpots Total Win", "totalWins": "Total Returns", "GGR": "Total GGR", "rtpPerc": "RTP %", "debits": "Debits", "credits": "Credits"}, "CSV": {"playerCode": "Player ID", "debits": "Debits", "credits": "Credits", "currency": "<PERSON><PERSON><PERSON><PERSON>", "playedGames": "Played Games", "totalBets": "Total Stakes", "totalWins": "Total Returns", "totalJpWins": "Jackpots Total Win", "GGR": "Total GGR", "RTP": "RTP %"}}, "REPORT_CURRENCY": {"note": "Please, select an Operator to proceed with search", "filterNote": "Please specify Date within one month period. You can see only history values for the past 3 months", "GRID": {"bets": "Total Stakes", "betsUsd": "Total Stakes (USD)", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_placeholder": "USD, EUR...", "date": "Date", "freebetWins": "Free Bets Win", "freebetWinsUsd": "Free Bets Win (USD)", "GGR": "Total GGR", "ggrUsd": "Total GGR (USD)", "jpWins": "Jackpots Win", "jpWinsUsd": "Jackpots Win (USD)", "playedGames": "Played Games", "totalJpWins": "Jackpots Total Win", "winnings": "Total Returns", "winningsUsd": "Total Returns (USD)"}}, "REPORT_FINANCIAL": {"GRID": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_placeholder": "USD, EUR...", "date": "Date", "to": "To", "from": "From", "initiatorName": "Initiator name"}, "FILTER": {"type": "Operation Type", "path": "Reseller / Operator (required)", "credit": "Credit", "debit": "Debit"}}, "REPORT_RG": {"GRID": {"player_code": "Player ID", "suspension_type": "Suspension type", "suspension_type_timeout": "Timeout", "suspension_type_exclusion": "Self-exclusion", "product_type": "Product Type", "product_type_casino": "Casino", "product_type_sports": "Sports", "product_type_undefined": "Undefined Type", "created_at": "Start Time", "end_time": "End Time"}}, "PAYMENTS_TRANSFERS": {"deposits_list": "Deposits List", "withdrawals_list": "Withdrawals List", "changeStatus": "Do you really want to change status to {{statusTitle}}?", "note": "Please, select an Operator to proceed with search", "GRID": {"path": "Reseller / Operator (required)", "cpTransferId": "CP Transfer ID", "trxId": "Transfer ID", "extTrxId": "extTrxId", "playerCode": "Player ID", "transferSource": "From", "transferDest": "To", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "playerBalanceAfter": "Balance After", "currencyCode": "<PERSON><PERSON><PERSON><PERSON>", "remainingBalance": "Remaining Balance", "updatedBalance": "Updated Balance", "startDate": "Start Date / Time", "endDate": "End Date / Time", "orderStatus": "Status", "processedBy": "Processed By", "paymentMethodCode": "PSP", "isOnline": "Is online", "orderId": "Payment ID", "approved": "Approved", "declined": "Declined", "blocked": "Blocked", "init": "Init"}, "CSV": {"trxId": "Transfer ID", "playerCode": "Player ID", "from": "From", "extTrxId": "extTrxId", "amount": "Amount", "playerBalanceAfter": "Balance After", "currencyCode": "<PERSON><PERSON><PERSON><PERSON>", "to": "To", "startDate": "Start Date / Time (GMT {{zone}})", "endDate": "End Date / Time (GMT {{zone}})", "orderStatus": "Status"}}, "GGR (EUR)": "", "HEADER": {"Home": "Home", "Logout": "Logout", "subTitle": "FALCON", "suffix": "Pro", "title": "SKYWIND"}, "LANGUAGE": {"Chinese": "中文", "English": "English", "Japanese": "日本語"}, "LANGUAGE_CODE": {"Chinese": "中文 (ZH)", "English": "English (EN)", "Japanese": "日本語 (JA)", "ChineseTraditional": "繁體中文 (ZH-TW)", "Malay": "Bahasa Malaysia (MS)", "Korean": "한국어 (KO)", "Thai": "Thai (TH)", "Vietnamese": "<PERSON><PERSON><PERSON><PERSON> (VI)", "Indonesian": "Bahasa Indonesia (ID)", "Romanian": "Romanian (RO)", "Italian": "<PERSON><PERSON> (IT)", "Greek": "Ελληνικά (EL)"}, "TWOFA": {"title": "Two-Factor Authentication", "subTitle_confirmation": "Confirmation required", "subTitle_pleaseSelect": "Please select preferred method below to enable two-factor authentication", "subTitle_pleaseSelectChange": "Please select preferred method below to use two-factor authentication", "subTitle_setup": "Setting up Two-Factor Authentication", "sms_pleaseEnterPhoneNumber": "Please enter your phone number", "sms_pleaseEnterCode": "Please enter your authentication code sent to phone number {{number}}", "sms_invalidNumber": "Entered phone number is invalid, please use only digits and a plus sign", "email_pleaseEnterCode": "Please enter your authentication code sent to e-mail {{email}}", "googleAuth_pleaseEnterCode": "Please enter your Google Authenticator code for login {{email}}", "googleAuth_scanThisQRCode": "Please scan this QR Code in your Google Authenticator app", "googleAuth_showStringSecretKey": "Show secret key as string", "googleAuth_useStringSecretKey": "... or enter this key instead", "setupConfirm_postfix": "to be sure that setup is complete", "phoneNumber": "Phone number", "code": "Code", "submit": "<PERSON><PERSON>", "next": "Next", "send": "Send", "youCanChangeType": "You can @change@ current confirmation method", "twofa_googleAuth": "Google Authenticator", "twofa_phoneBySMS": "Phone (by SMS)", "twofa_email": "E-Mail", "validationError_codeInvalid": "Entered code is invalid. Only digits are allowed.", "error_code716": "Entered code is invalid. Please try again. (error code 716)", "error_code717": "Authentication session has expired. Please try again. (error code 717)", "error_code718": "Unable to authenticate. Please try again. (error code 718)", "error_code719": "Unable to send verification SMS. Please try again later. (error code 719)", "error_code720": "Unable to send verification E-Mail. Please try again later. (error code 720)", "error_code721": "Unable to authenticate using selected two-factor auth type. Please contact customer support. (error code 721)", "error_code722": "Unable to authenticate. Please try again later or contact customer support. (error code 722)", "error_code723": "Unable to authenticate. Please try again later or contact customer support. (error code 723)"}, "PASSWORD_CHANGE": {"passwordCurrent": "Current password", "passwordNew": "New password", "passwordConfirm": "Confirm new password", "passwordRequirements": "Your new password should", "passwordNotMatch": "The new password and the new password confirmation don't match.", "passwordMinLength": "Be at least 8 characters long", "passwordContainDigit": "Contain at least {{value}} digit", "passwordContainLowercase": "Contain at least {{value}} lowercase letter", "passwordContainUppercase": "Contain at least {{value}} uppercase letter", "passwordChange": "Change password", "passwordChangedSuccessfully": "Password was changed successfully"}, "ADD_GAMES_CASCADE": {"title": "Add Games Cascade {{name}}", "hintText": "In this area you can add multiple games at once for selected part of business structure.\nAdded games will be applied for selected entity and its own children.", "selectedEntity": "Selected entity:", "availableGames": "Available games:", "noGamesSelected": "No selected games", "btnAddNGames": "Add {{ count }} games", "gamesAddedSuccessfully": "{{ count }} games were added successfully!", "btnSave": "Save"}, "ENTITY_SETUP": {"REGIONAL": {"MODALS": {"path": "Reseller / Operator", "entityPath": "Business entity path", "selectTransactionDirection": "Select transaction direction", "placeholderSearch": "Search", "btnCancel": "Cancel", "btnApply": "Apply", "btnAdd": "Add", "addNewCountry": "Add New Country", "chooseCountry": "Choose Country", "selectJurisdiction": "Select Jurisdiction", "noJurisdiction": "No Jurisdiction", "manageBalance": "Manage Balance", "manageLanguages": "Manage Languages", "manageJurisdictions": "Manage Jurisdictions", "manageCurrency": "Manage Currency", "manageCountry": "Manage Country", "selectedCurrency": "Selected Currency", "chooseCurrency": "<PERSON><PERSON>", "addCurrency": "Add <PERSON>cy", "balance": "Balance", "yourBalance": "Your Balance", "parentBalance": "Parent Balance", "transfer": "Transfer", "available": "Available", "availableToDeposit": "Available amount to deposit", "availableToWithdraw": "Available amount to withdraw", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "creditFrom": "Credit from", "debitTo": "Debit to", "optional": "(optional)", "selected": "Selected", "totalBalance": "Your total Balance will be", "error_noCountries": "No Countries Available!", "error_noCurrencies": "No Currencies Available!", "error_balanceMin": "Please, put a positive value", "error_balanceNotEquals": "Please, put a positive value", "notificationCountryAdded": "Country \"{{name}}\" was added", "notificationCountriesAdded": "Countries list has been updated", "notificationCountryRemoved": "Country was removed", "notificationCurrenciesAdded": "Currencies list has been updated", "notificationUserAdded": "User \"{{name}}\" was created", "notificationUserUpdated": "User \"{{name}}\" was modified", "notificationCurrencyBalanceAdded": "Currency {{currency}} with balance {{amount}} {{currency}} was added to entity {{entity}}", "notificationCurrencyAdded": "Currency {{currency}} was added to entity {{entity}}", "notificationBalanceSubtracted": "{{amount}} {{currency}} was subtracted from {{entity}}", "notificationBalanceAdded": "{{amount}} {{currency}} was added to {{entity}}", "jurisdictionsErrorLabel": "At least one jurisdiction should be selected"}, "tabname": "Regional", "useJurisdictionsDataOnly": "Use allowed/restricted/default country of the jurisdiction", "titleAllowedCountries": "Allowed Countries", "titleRestrictedCountries": "Restricted Countries", "titleCountries": "Countries", "titleCurrencies": "Currencies", "titleLanguages": "Languages", "titleJurisdictions": "Jurisdictions", "titleDeploymentGroups": "Deployment groups", "add": "Add", "manage": "Manage", "countryName": "Name (Code)", "jurisdiction": "Juris<PERSON>", "selectAll": "Select All", "unSelectAll": "Unselect All", "currencyName": "Name (Code)", "balance": "Balance", "languageName": "Name (Code)", "jurisdictionName": "Name (Code)", "default": "<PERSON><PERSON><PERSON>", "setDefault": "<PERSON>", "detachDefaultGroup": "Detach Group", "actionDelete": "Delete", "btnConfirmDelete": "Confirm Delete", "btnClose": "Close", "pleaseConfirm": "Please confirm your action", "languagesWereAdded": "Language list was updated successfully!", "regionalsWereAdded": "Regional items list was updated successfully!", "currencyDeleted": "Currency was successfully removed", "deploymentGroupDescription": "Description", "deploymentGroupRoute": "Route"}, "USERS": {"MODALS": {"createUser": "Create User", "editUser": "Edit User", "userCreated": "User was successfully created", "userModified": "User was successfully modified", "secretKey": "Secret Key", "btnClose": "Close", "btnSave": "Save Changes", "confirmationRequired": "Confirmation Required", "unblockUserModalTitle": "User Unblock", "btnUnblock": "Unblock", "userLockedLoginTillDate": "User \"{{username}}\" is locked", "userLockedChangePasswordTillDate": "Password change is locked till", "pleaseConfirm": "Please confirm your action", "pleaseSelectAndConfirm2FAReset": "Please select auth types which needs to be reset and confirm submit", "btnConfirmReset": "Confirm Reset", "btnConfirmDeleteUser": "Confirm Delete", "twofaUserResetNotification": "You are trying to reset two-factor authentication settings for user {{username}}", "deleteUserNotification": "You are trying to delete user account \"{{username}}\"", "firstname": "First Name", "lastname": "Last Name", "username": "Username", "password": "Password", "phoneNumber": "Phone number", "email": "E-Mail", "status": "Status", "userType": "Account Type", "roles": "Roles", "additionalroles": "Additional Roles"}, "tabname": "Users", "entity": "Entity", "name": "Name", "username": "Username", "role": "Role", "email": "E-mail", "created": "Created", "modified": "Modified", "status": "Status", "statusActive": "Active", "statusInactive": "Inactive", "locked_by_auth": "Locked", "userTypeBO": "Back-office access", "userTypeOperatorAPI": "API access", "userOperatorNoType": "No Type", "editUserAction": "Edit User", "deleteUserAction": "Delete User", "reset2faAction": "Reset 2FA Settings", "btnCreateUser": "Create User", "searchPlaceholder": "Search", "twofaResetSuccessfull": "Two-Factor settings was cleared for user \"{{ username }}\"", "userRemoved": "User \"{{username}}\" was removed", "userUnblocked": "User \"{{ username }}\" was unblocked", "boUserPasswordExpires": "Password expires every:", "apiUserPasswordExpires": "Password never expires.", "changePasswordNote": "IMPORTANT NOTE: You are trying to change the password for API User!\nPlease do not forget to change it at the integration. Please contact SKYWIND SUPPORT with any questions.", "changeUserTypeNote": "IMPORTANT NOTE: You are trying to change the User type from API access to Back Office access!\nThis action may cause integration issues. Please contact SKYWIND SUPPORT with any questions.", "PERIOD_TYPE": {"minutely": "minutely", "hourly": "hourly", "daily": "daily", "weekly": "weekly", "monthly": "monthly", "yearly": "yearly"}}, "NOTIFICATIONS": {"tabname": "Notifications", "changesUnsavedNotifications": "Do you really want to leave this page? All unsaved changes will be lost.", "savedSuccess": "Notifications were successfully saved", "addNew": "Add new", "save": "Save", "removeItem": "Remove item", "emptyMessage": "No items set", "emailFrequency": "Email frequency", "sendEmailsFrom": "Send emails from", "sendEmailsTo": "to", "notificationDateError": "One of the fields must be filled", "LOWBALANCE": {"tabTitle": "Low Balance Notifications", "EMAIL": {"title": "Low balance mail notifications", "placeholder": "Please insert email", "description": "Notification in case of low balance can be sent to a subscribed contact"}, "PHONE": {"title": "Low balance phone notifications", "placeholder": "Please insert phone number", "description": "Notification in case of low balance can be sent to a subscribed contact"}}, "PROMO": {"title": "Promotion notifications", "placeholder": "Please insert email", "description": "Receive promotion notifications for these email addresses"}, "ENGAGEMENT": {"tabTitle": "Engagement Tools Notifications", "title": "Engagement Tools Notifications", "placeholder": "Please insert email", "description": "Send Engagement Tools reports to these email addresses:"}}, "GAMES": {"MODALS": {"editGames": "Edit Games", "btnCancel": "Cancel", "btnPrevious": "Previous", "btnNext": "Next", "btnApply": "Apply", "btnFinish": "Finish", "selectGames": "Select Games", "newGamesSetup": "New Games Setup", "preview": "Preview", "selectAll": "Select all Games", "gameName": "Game Name", "gameReady": "Game is ready to save", "ready": "Ready", "noLabels": "No labels", "setRoyalties": "Set Royalties", "selectStatus": "Select Status", "titleConfirmation": "Confirmation Required", "messageFailedSingle": "Failed to remove entity game <strong>{{title}}</strong> (code: {{code}}) which can also be added to entity children.", "messageConfirmRemove": "Please confirm forced removal", "messageFailedMultiple": "Failed to remove games which are also added to entity children", "cancelRemove": "Cancel remove", "confirmForceRemove": "Confirm Force Remove", "aamsCode": "AAMS Code", "mustWinJackpotBundled": "MWJP Bundled", "externalGameId": "External Game ID", "specificGamesAlert": "Please note that the current entity has game limits filter which applies to all games, however the following games are \"special games\" and the filter will not apply to them:", "kill_sessions": "Are you sure? All active sessions of this game will be killed"}, "MANAGE_GRID": {"providerTitle": "Game Provider", "title": "Game Name", "code": "Game Code", "isFreebetSupported": "Freebet Supported", "isBonusCoinsSupported": "Bonus Coins Supported", "isMarketplaceSupported": "Marketplace Supported", "isCustomLimitsSupported": "Сustom Limits Supported", "limitFiltersSupported": "Limit Filters Supported", "aamsCode": "AAMS Code", "mustWinJackpotBundled": "MWJP Bundled"}, "SET_JACKPOT": {"title": "Set Jackpot", "create": "Create", "type": "Jackpot type", "set-notification": "Jackpot was set for {{ gameCode }} game"}, "tabname": "Games", "generalGameInfoTabName": "General Games Info", "jpGameInfoTabName": "JP Games Info", "createGame": "Create Game", "cloneGame": "Clone Game", "editGame": "Edit Game", "setJp": "Set Jackpot", "clone": "<PERSON><PERSON>", "add": "Add", "exportConfig": "Export Config", "btnManageGames": "Manage Games", "searchPlaceholder": "Search by name or code...", "title": "Name", "providerTitle": "Provider", "code": "Code", "royalties": "Royalties", "labels": "Labels", "status": "Status", "type": "Type", "special": "Special game", "connectedPool": "Connected Pool/JackpotId", "notificationSingleGameRemoved": "{{amount}} game was removed", "notificationMultipleGamesRemoved": "{{amount}} games were removed", "notificationSingleGameAdded": "{{amount}} game was added", "notificationMultipleGamesAdded": "{{amount}} games were added", "notificationGameCreated": "\"{{game}}\" game was created successfully", "notificationGameUpdated": "\"{{game}}\" game was updated successfully", "notificationGameAddedToEntity": "\"{{code}}\" game was added to entity successfully", "gameCode": "Game code", "gameTitle": "Game title", "url": "Url", "addGameToEntity": "Add game to entity", "entityPath": "Business entity path", "CSV": {"gameCode": "Game code", "billingConfigurationLevel": "Billing Configuration Level", "businessEntityConfigurationLevel": "Business Entity Configuration Level", "isInherited": "Is Inherited", "configuredOn": "Configured On", "jpId": "JP <PERSON>", "jpType": "JP Type", "gameName": "Game name", "jpGame": "JP game", "connectedPool": "Connected Pool/JackpotId", "status": "Status"}}, "WHITELISTING": {"MODALS": {"editSite": "Edit Available Site", "addSite": "Add Available Site", "siteUrl": "Site URL", "default": "<PERSON><PERSON><PERSON>", "siteTitle": "Site Title", "operatorSiteGroupName": "Operator Site Group Name", "externalCode": "External Code", "status": "Status", "btnClose": "Close", "btnSave": "Save Changes", "ok": "OK", "statusLog": "Status log", "confirmSetAsDefault": "Are you sure you want to set \"{{url}}\" site as default?", "successSetAsDefault": "Site \"{{url}}\" was successfully set as default"}, "tabname": "Whitelisting", "boIpWhitelist": "Game launch IP Whitelist", "userIpWhitelist": "BO & API IP Whitelist", "btnRemoveSelected": "Remove Selected", "ipAddress": "IP Address", "actions": "Actions", "whitelistNotCreated": "Whitelist is not created", "noItemsFound": "No items found", "placeholderEnterIP": "Enter IP Address", "placeholderIssueId": "Enter Issue ID", "actionRemove": "Remove item", "actionEdit": "Edit item", "btnAdd": "Add", "availableSites": "Available Sites", "btnAddSite": "Add Site", "siteStatusActive": "Active", "siteStatusInactive": "Inactive", "siteStatusInfo": "Info", "gridSiteTitle": "Name", "entity": "Entity", "gridSiteUrl": "Site URL", "gridSiteStatus": "Status", "operatorSiteGroupName": "Operator Site Group Name", "externalCode": "External Code", "isDefault": "<PERSON><PERSON><PERSON>", "title": "Use Site Authorization", "notificationRemoved": "Site was successfully removed", "notificationSaved": "Site was successfully saved", "notificationAdded": "Site was successfully added", "notificationChanged": "Status was successfully changed", "updateWhitelist": "IP Whitelist was successfully updated", "removedWhitelist": "IP was successfully removed", "notificationSelectedRemoved": "Selected sites were successfully removed", "confirmationRemoved": "Do you really want to delete this site?", "confirmationSelectedRemoved": "Do you really want to delete selected sites?", "removedWhitelistConfirmation": "Do you really want to delete this addresses?", "checkWebsiteWhitelisted": "Check WebSite Whitelisted", "activate": "Activate", "deactivate": "Deactivate", "remove": "remove"}, "PLAYER_BLOCKLIST": {"tabname": "Player Blocklist", "playerBlocklist": "Player Blocklist", "btnRestorePlayers": "Restore Selected Players", "btnSuspendPlayer": "Suspend Player", "placeholderEnterCode": "Enter Player Code", "playerCode": "Player Code", "actions": "Actions", "actionRestore": "Rest<PERSON>"}, "TWOFA_SETUP": {"tabname": "Security", "twofaStatusPrefix": "Two-Factor Authentication is", "twofaStatusActive": "Active", "twofaStatusInactive": "Inactive", "btnActivate": "Activate", "btnDeactivate": "Deactivate", "typeStateEnabled": "Enabled", "typeStateDisabled": "Disabled", "twofaTypeSMS": "SMS", "twofaTypeEMail": "E-Mail", "twofaTypeGoogleAuth": "Google Authenticator", "btnSave": "Save Changes", "emailTemplateFrom": "From", "emailTemplateSubject": "Subject", "emailTemplateBody": "HTML Template", "smsTemplateBody": "Text template", "useDefault": "Use default template", "exampleTemplate": "Example template", "availableVariables": "Available variables", "NOTIFICATIONS": {"activated": "2FA was successfully activated", "deactivated": "2FA was successfully deactivated", "saved": "2FA Settings successfully saved!", "inactive": "Two-Factor Authentication is inactive because of all auth types are disabled"}}, "DOMAINS": {"maintenanceUrl": "Maintenance URL", "maintenancePlaceholder": "URL format: https://example.com", "maintenanceUrlEditSuccess": "Maintenance URL was successfully edited", "maintenanceUrlConfirmation": "Do you really want to save maintenance URL?", "entityDomains": "Default Domains", "btnApply": "Apply", "tabname": "Domains", "domains": "Domains", "domain": "Domain", "static": {"static": "Game", "lobby": "Lobby", "live-streaming": "Live Streaming", "ehub": "E-Hub", "tags": "Game domain tags"}, "dynamic": "Dynamic", "pools": "Domains pool", "pool": "Static domains pool", "notSet": "Not set", "set": "Set", "reset": "Reset", "environment": "Environment", "btnYes": "Yes", "btnNo": "No", "formTitle": "Auth by Domain (one per line)", "textareaPlaceholder": "Domains", "btnCancel": "Cancel", "selectDomain": "Select domain", "inherited": "inherited", "modalTitle": "Select {{ type }} Domain for Entity", "modalPoolTitle": "Select Domains Pool for Entity", "saved": "Entity domains was successfully saved!", "dynamicDomainsActivated": "Dynamic domains routing is activated", "dynamicDomainsDeactivated": "Dynamic domains routing is deactivated", "dynamicDomainsLabel": "Dynamic domain routing", "parentDomains": "Parent (inherited) domains", "tags": "Tags", "addTag": "Add tag", "saveTags": "Save tags", "resetTags": "Reset tags", "tagsSaved": "Tags saved", "tagsReset": "Tags reset"}, "PROXY": {"merchantParams": "Merchant Parameters", "merchantConfirmation": "Do you really want to save merchant proxy?", "merchantProxy": "Merchant proxy", "btnSaveProxy": "Save Proxy", "proxyTabName": "Proxy", "titleProxyAdd": "Add new proxy", "btnSaveChanges": "Save Changes", "btnAddNewProxy": "Add New", "btnCancelProxy": "Cancel", "tittleProxy": "Tittle", "decsriptionProxy": "Description", "proxyManagementLabel": "Proxy Management", "urlLabel": "Url", "descriptionLabel": "Description", "urlPlaceholder": "URL format: https://example.com", "labelNotSet": "Not Set", "btnSet": "Set", "labelSelectProxyForEntity": "Select Proxy for Entity", "placeholderSelectProxy": "Select proxy...", "btnRemove": "Remove"}, "RATES": {"tabname": "Bonus Coin", "bonusCoinRatesTitle": "Bonus Coin", "btnSave": "Save Changes", "bnsRequiredForPromotions": "Bonus Coin balance required for promotions", "NOTIFICATIONS": {"bonusCoinsRatesSaved": "Bonus coin rates was saved successfully!"}}, "ADDITIONAL": {"additional": "Additional", "selectType": "Select type", "unfinished": "Unfinished", "all": "All", "paymentRetrySettings": "Payment retry settings", "minPaymentRetryTimeout": "Min payment retry timeout (seconds)", "maxRetryAttempts": "Max retry attempts", "maxSessionTimeout": "Max session timeout. (minutes)", "maxPaymentRetryAttempts": "Max payment retry attempts", "gameLogoutOptions": "Game logout options", "notificationPaymentRetry": "Max payment retry attempts value was successfully updated", "notificationPaymentRetrySettings": "Payment retry settings were successfully updated", "notificationLogoutOptions": "Game logout options were successfully saved"}, "GAME_LIMITS": {"addLimits": "Add Limits", "tabName": "Game Limits", "createLimitsTitle": "Add New Custom Game Limits", "editLimitsTitle": "Edit Custom Game Limits", "cloneLimitsTitle": "Clone Custom Game Limits", "customLimitsTitle": "Custom Game Limits", "entityLabel": "Entity", "gameGroupLabel": "Game Group", "notSet": "Not set", "gameLabel": "Game", "currencyLabel": "<PERSON><PERSON><PERSON><PERSON>", "addCurrency": "Add <PERSON>cy", "limitsUpdated": "Game limits was successfully updated", "limitsCloned": "Game limits was successfully cloned", "removeGameMessage": "Do you really want to remove game from game group?", "gameRemoved": "Game was successfully removed from game group", "editLimitsTooltip": "Edit limits", "cloneLimitsTooltip": "Duplicate limits", "removeGameTooltip": "Remove game from game group", "limitsAlreadyExist": "Game limits already exists. Click \"Save\" to overwrite limits or \"Cancel\" if you want to leave existing ones. ", "from": "From", "to": "To", "showGameLimits": "Custom game limits", "exportToExcel": "Export all bet limits to Excel", "flatReportNotFound": "Limits report not found for this entity", "flatReportWasExported": "Limits report was successfully exported to Excel", "noLimitsFound": "No limits found for this game within current game group", "notSelected": "Please select game group and game to show limits", "defaultGameGroupLabel": "Default game group", "noLimitsAvailable": "No Custom game limits available", "TABLE": {"gameGroup": "Game Group", "game": "Game", "currency": "<PERSON><PERSON><PERSON><PERSON>", "coinBets": "Coin Bets", "actions": "Actions", "totalBetMultiplier": "Total Bet Multiplier", "minCoinBet": "Min Coin Bet", "minTotalBet": "Min Total Bet", "maxCoinBet": "Max Coin Bet", "maxTotalBet": "Max Total Bet", "stakeDef": "<PERSON><PERSON> Def", "remove": "remove"}}, "GAME_GROUP": {"tabName": "Game Groups", "description": "Description", "descriptionPlaceholder": "Enter game group description", "id": "Id", "isDefault": "<PERSON><PERSON><PERSON>", "inherited": "Inherited", "isDefaultPlaceholder": "Default game group", "actions": "Actions", "name": "Name", "namePlaceholder": "Enter game group name", "notificationDefaultGameGroupChanged": "{{gameGroup}} game group was successfully set as default", "notificationCreated": "\"{{gameGroup}}\" game group was successfully created", "notificationEdited": "\"{{gameGroup}}\" game group was successfully updated", "removeGameGroupMessage": "Do you really want to remove \"{{gameGroup}}\" game group?", "gameGroupRemoved": "\"{{gameGroup}}\" game group was successfully removed", "editGameGroupTooltip": "Edit game group", "removeGameGroupTooltip": "Remove game group", "createGameGroupTitle": "Create Game Group", "editGameGroupTitle": "Edit Game Group", "addGameGroup": "Add Game Group", "FORM": {"gameGroup": "Default game limits group"}, "MODAL": {"confirmMessage": "Do you really want to delete this game group?", "labelAddFlag": "Add force flag", "messageDefaultGameGroup": "Game group is used as default. Operation requires force flag.", "messageFlag": "If you want to delete it please add force flag."}}, "GAME_GROUP_FILTERS": {"tabName": "Game Limit Filters", "note": "Note, if the same game appear in two different filters of the same game group and currency, the last updated filter will be applied", "GRID": {"id": "#", "createdAt": "Date Created", "updatedAt": "Date Updated", "gameGroup": "Game Group", "minTotalBet": "Min Total Bet", "maxTotalBet": "Max Total Bet", "maxExposure": "Max Exposure", "defaultTotalBet": "Default Total Bet", "winCapping": "Win Capping", "currencies": "Currencies", "games": "Games", "inherited": "Inherited", "newFilter": "New Filter", "editTooltip": "Edit Game Limits Filter", "deleteTooltip": "Delete Game Limits Filter", "confirmMessage": "Do you really want to delete this Game Limits Filter?"}, "FORM": {"createGameGroupFilterTitle": "Add Game Limits Filter", "editGameGroupFilterTitle": "Edit Game Limits Filter", "gameGroup": "Game Group", "defaultGroupLabel": "Default group", "minTotalBet": "Min Total Bet (in Euro)", "maxTotalBet": "Max Total Bet (in Euro)", "maxExposure": "Max Exposure (in Euro)", "defaultTotalBet": "Default Total Bet (in Euro)", "winCapping": "Win Capping (in Euro)", "currencies": "Currencies", "appliesToAllCurrenciesLabel": "Filter applies to all currencies", "appliesToSpecificCurrenciesLabel": "Filter applies to specific currencies", "games": "Games", "appliesToAllGamesLabel": "Filter applies to all slot games", "appliesToAllGamesLabelNote": "Note! the filter will not be applied to the following games: ", "appliesToSpecificGamesLabel": "Filter applies to specific slot games", "appliesToSpecificGamesLabelNote": "Note, special games which their limits cannot be customized do not appear in the list below"}, "NOTIFICATIONS": {"created": "Game Limits Filter successfully created", "updated": "Game Limits Filter successfully updated", "deleted": "Game Limits Filter successfully deleted"}}, "LABELS": {"title": "Entity Labels", "labelsGroup": "Labels Group", "labels": "Labels", "addLabelsGroup": "Add labels group", "updated": "Entity labels successfully updated"}, "RTP_REDUCER": {"tabName": "RTP Reducer", "entity": "Entity", "inheritedForm": "Inherited from", "changeDateTime": "Change Date Time", "gameName": "Game name", "gameCode": "Game code", "rtpBefore": "% Theoretical RTP", "rtpAfter": "% New RTP", "rtpDeduction": "% RTP Deduction", "modalTitle": "New RTP Reduction - {{ title }}", "alertModalRtp": "This action will affect {{ title }} and all of its descendants", "selectGamesChange": "Select the games to change", "selectRtpReduce": "Select the RTP to reduce", "btnCreateRtp": "New RTP configuration", "warningRtp": "List of games with incorrect RTP", "updatedRtd": "New RTP deduction was applied", "removerRtd": "RTP deduction was removed", "filterTitle": "Game name / code", "showChanges": "Show changes only", "MODAL": {"gameName": "Game name", "gameCode": "Game code", "currentRtp": "Theoretical RTP", "newRtp": "New RTP", "resetMessage": "Do you really want to reset RTP for {{ title }} ?"}}, "EMAIL_TEMPLATE": {"tabName": "Email Templates", "templateUpdated": "Template was successfully updated"}, "ENGAGEMENT": {"tabName": "Engagement", "save": "Save", "PAYMENT_SETTINGS": {"tabName": "Payments Settings", "title": "Bonus Payment Settings", "placeholder": "Bonus Payment Method", "inherited": " (inherited)"}, "SUPPORTED_PAYMENT_SETTINGS": {"placeholder": "Supported Bonus Payment Method"}, "BONUS_COIN_SETTINGS": {"tabName": "Bonus Coin Settings", "title": "Bonus Coin Settings", "conversionRate": "Conversion Rate"}, "NOTIFICATIONS": {"paymentMethodChanged": "Payment method was changed successfully!"}}, "BREADCRUMBS": {"selectEntity": "Select Entity", "noEntities": "No entities found", "copy": "Copy path to selected entity", "collapse": "Collapse breadcrumbs"}, "TEST_PLAYERS": {"tabName": "Test Players", "GRID": {"code": "Player Code", "source": "Source", "startDate": "Start Date", "endDate": "End Date", "newTestPlayer": "New Test Player", "support": "Support", "integration": "Integration", "deleteTestPlayer": "Delete Test Player"}, "createTestPlayer": "Create Test Player", "editTestPlayer": "Edit \"{{code}}\" Test Player", "code": "Player Code", "startDate": "Start Date", "endDate": "End Date", "notificationCreated": "Test player was successfully created", "notificationEdited": "\"{{code}}\" test player was successfully updated", "codeShouldBeUnique": "Player code should be unique per entity", "autoCreateTestJackpot": "Auto create test jackpot instances", "playersAreSuccessfullyMarkedAsTest": "All players of entity were successfully marked as test players", "playersAreNoLongerMarkedAsTest": "All players of the entity are no longer marked as test players", "autoCreateTestJackpotStrategyWasSetSuccessfully": "Strategy for test jackpots creation was successfully set to auto", "autoCreateTestJackpotStrategyWasUnsetSuccessfully": "Auto strategy for test jackpots creation was successfully unset", "maxTestPlayers": "Maximum test players", "changeMaxTestPlayersConfirmation": "Do you really want to save maximum test players amount?", "maxTestPlayersEditSuccess": "Maximum test players amount was successfully edited", "maxTestPlayersSmallerThanTotal": "Maximum test players value shouldn't be smaller than the total number of test players the entity has", "deleteTestPlayerConfirmation": "Do you really want to delete \"{{playerCode}}\" test player?", "testPlayerSuccessfullyDeleted": "\"{{playerCode}}\" test player was successfully deleted"}, "toBusinessStructure": "to Business Structure", "placeholderChooseOption": "Choose any option"}, "PROMO": {"CUSTOMER_BULK_ACTIONS": {"applyBonusCoins": "Apply Bonus Coins", "applyFreeBets": "Apply Free Bets"}, "NOTIFICATIONS": {"PROMO_CREATED": "Promotion {{ title }} was successfully created", "PROMO_UPDATED": "Promotion {{ title }} was successfully updated"}, "STEPS": {"details": "Promo Details", "freeBetsSetup": "Free Bets Setup"}, "MODALS": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "promoName": "Promo Name", "promoType": "Promo Type", "startDateTme": "Start Date/Time", "endDateTme": "End Date/Time", "freeSpinsAmount": "Free Spins Amount", "btnAddGames": "Add Games", "btnApplySelected": "Apply Selected", "placeholderSelectGames": "Select a game...", "selectGames": "Select Games", "pleaseAddGames": "Please add some games", "errorGamesRequired": "Games are required", "gameName": "Game Name"}, "REPORTS": {"playerCode": "Player ID", "title": "Title", "receivedAt": "Date Received", "amountReceived": "Amount received", "amountAwarded": "Amount awarded", "amountRemaining": "Amount remaining", "expirationDate": "Expiry date", "status": "Status", "addExtraReward": "Add <PERSON>", "ADD_MORE_REWARDS": {"title": "Add <PERSON>s", "addMoreReward": "Add <PERSON>", "prolongExpiration": "Prolong Payout Expiration", "changesPreview": "Changes Preview", "appliedChanges": "Applied Changes", "updatedExpirationDate": "Updated Expiry Date", "updatedAmountAwarded": "Updated Amount Awarded", "updatedAmountRemaining": "Updated Amount Remaining", "rewardWasApplied": "<PERSON><PERSON> was applied to player {{code}} successfully", "pleaseNoteDelayInReport": "Your changes are applied. Please note that report is being updated within an hour. You will see your changes after it updates"}}, "EDIT": {"GENERAL": {"title": "General", "placeholderTitle": "Enter Promotion title", "placeholderDescription": "Enter Promotion description", "startDate": "Start Date", "endDate": "End Date", "funding": "Funding", "HINTS": {"funding": "Operator funding: BNS transactions are included in GGR real money reports.\nSkywind funding: BNS transactions are excluded from GGR real money reports.", "operator": "The parent of the chosen entity will have access to manage this promotion."}}, "REWARDS": {"title": "Rewards", "type": "Reward Type", "typeNotSet": "Not set", "noneSelected": "None selected", "rewardAmount": "<PERSON><PERSON> Amount", "rewardAmountPerPlayer": "<PERSON><PERSON> Amount", "amount": "Amount", "percent": "Percent", "rewardBonus": "Reward Bonus", "rewardLimit": "<PERSON><PERSON>", "minLimit": "Min limit", "maxLimit": "Max limit", "in": "in", "on": "on", "per": "per", "days": "days", "rewardProcessing": "Reward Processing", "expiration": "Expiration", "expirationPeriodType": "expiration period type", "qualifyingGames": "Qualifying Games", "PLACEHOLDERS": {"egNorM": "eg. {{n}} or {{m}}", "egN": "eg. {{n}}"}, "HINTS": {"rewardBonus": "** Test text, please replace", "rewardAmount": "Amount for a player offered in bunds of promotion", "rewardLimit": "** Test text, please replace", "payoutLimitPerPlayer": "Limits marketing payouts for a player", "promoPayoutLimit": "Shows the upper border of marketing payouts all customers can get from this promo for a preset period of time", "maxConversionAmount": "** Test text, please replace", "expiration": "After expiration, any unused rewards are lost and the promotion is over for the player.", "expirationBNS": "Starting from when the player receives the Bonus Coins, they will have this many days/hours to use the Bonus Coins before they expire.", "wageringMultiplier": "** Test text, please replace", "numberOfFreeBets": "The # of free bets awarded to each player in the promotion", "coinValue": "This value is calculated according to the preset exchange ratio", "playThrough": "Amount of times player must make bets from payout before it can be withdrawn", "payoutSchedule": "Schedule how rewards should be paid", "numberOfBonusCoins": "How many Bonus Coins will be awarded to each player", "minCoinsToRedeem": "A player is required to have at least this many Bonus Coins in order to redeem the coins into real money", "maxCoinsToRedeem": "Set the max amount of BNS each player is allowed to redeem. Any BNS winnings above this amount will be capped."}, "VIRTUAL_MONEY": {"maxConversionAmount": "Max conversion amount", "wagering": "Virtual Money wagering", "multiplier": "Multiplier", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "bonusAmount": "Bonus Amount", "hintText": "Virtual money cannot be withdrawn by a customer. To withdraw virtual money, the player must convert it to real money first. Conversion is automatic and takes place immediately after bonus wagering criteria are met"}, "FREEBET": {"numberOfFreeBets": "Number of Free Bets", "coinValue": "Coin Value", "hintText": ""}, "REBATE": {"rewardCurrecy": "<PERSON><PERSON>", "pleaseSelect": "Please select", "type": "Type", "typeGGR": "% from GGR (Bets – Winnings)", "typeNGR": "% from NGR (Bets – Winning – Marketing Payouts)", "typeMinMax": "Minimal and/or Maximal amount of Bets", "payoutPeriodTypeHours": "Hours", "payoutPeriodTypeDays": "Days", "payoutPeriodTypeWeeks": "Weeks", "payoutPeriodTypeMonths": "Months", "playThrough": "Play through", "payoutSchedule": "Payout schedule", "intervalTypeDaily": "Daily", "intervalTypeWeekly": "Weekly", "intervalTypeMonthly": "Monthly", "lastDayOfMonth": "Last day of Month", "hintText": "", "payoutLimitPerPlayer": "Payout Limit per Player", "promoPayoutLimit": "Promotion Payout Limit"}, "BONUS_COIN": {"numberOfBonusCoins": "Bonus Coins per player", "totalValuePreview": "Total value preview", "coinValue": "Real money value", "coinValuesNotDefined": "Coin values are not defined! You need to set coin values in entity setup.", "minCoinsToRedeem": "Min. BNS required to redeem", "maxCoinsToRedeem": "Redeem cap", "realMoneyValue": "Real money value", "redemptionTitle": "Bonus Coins Redemption", "redemptionSubtitle": "Bonus Coins can be redeemed for real money once the player's BNS balance is above minimum requirement.", "estimatedRedeemPercent": "An estimated {{percent}}% of players will redeem", "hintText": "Bonus Coins can be used in qualifying games where winnings are paid out in alternative currency (BNS).", "startRewardOnPlayerAdded": "Start expiration when the player is added to the promotion", "startRewardOnGameOpen": "Start expiration on player's 1st game launch"}, "EXPIRATION_PERIOD": {"days": "days", "hours": "hours"}}, "CONDITIONS": {"title": "Conditions", "hint": "Use this screen to build that condition that triggers the bonus"}, "QUALIFYING_GAMES": {"title": "Qualifying Games", "virtualMoneyCoefficient": "<PERSON><PERSON><PERSON>.", "freebetCoinValue": "Coin value"}, "PARTICIPANTS": {"title": "Participants", "addParticipants": "Add Participants", "numberOfParticipantsSelected": "{{ playercodes.length }} new players will be added to promo participants", "unableToParsePlayerCodes": "Unable to parse player codes. Please check your recent CSV file and try again", "csvWrongFileType": "Wrong file type!", "csvWrongFileTypeExtra": "Only CSV files are allowed to upload.", "csvMaxNumberItemsExceeded": "Maximum number of participants ({{ limit }}) exceeded", "csvMaxCsvFileSizeExceeded": "The file size exceeds the allowable limit of", "labelAddCSV": "Add CSV File...", "btnAddSelected": "Add Selected", "csvHintStructure": "Please ensure that CSV has column \"Player ID\". It contains codes of players who will participate in this promo.", "downloadSampleCSV": "Download sample CSV", "fileWithPlayersCount": "with {{ length }} players", "unableToParseCSV": "unable to parse CSV", "removeThisCSV": "Remove this CSV file", "bonusCoinsBalance": "Bonus Coins balance", "rewardPerPlayer": "Reward per player", "currentParticipants": "Current participants", "participantsRevoked": "Revoked participants", "numberOfPlayersToSave": "Number of players to save", "maxNumberOfPlayers": "Max. number of players", "rewardValueNotDefined": "Value not defined", "rewardValueNotDefinedHint": "Please set up value for Number of Bonus Coins in Rewards area", "numberOfDuplicatesIgnored": "{{ length }} duplicates ignored", "insufficientBalance": "The BNS Balance in your account is insufficient to run a Bonus Coins promotion.\nPlease contact your Skywind distributor for information.", "removeParticipant": "Remove", "maximumLimitExceeded": "maximum limit exceeded", "participantRemoved": "Part<PERSON>pan<PERSON> was removed from promotion", "removeParticipantPrompt": "Are you sure you want remove player **{{ playerCode }}** from current promotion?", "removeParticipantPromptForce": "You are about to remove player **{{ playerCode }}** from current promotion! This action requires a force flag. Are you sure?", "errorPlayerHasPromo": "Player already have active @promotion@", "errorParticipantsGeneral": "Unable to add participants"}, "promotion": "Promotion", "panelInfo": "Info", "panelReports": "Reports", "addParticipants": "Add Participants", "projected": "Projected", "actual": "Actual", "created": "Created", "by": "by", "createdBy": "Created by", "reach": "Reach", "participants": "Participants", "payout": "Payout", "linkBonusCoinsPerformance": "Bonus Coins Performance Report", "linkPromotionParticipants": "Promotion Participants Report", "linkRedeemableExpiredCoins": "Redeemable Expired Coins Report"}, "GRID": {"title": "Promo Name", "id": "ID", "type": "Type", "status": "Status", "state": "State", "labels": "Labels", "participants": "Participants", "totalPayout": "Total Payout", "startDateTime": "Start Date / Time", "endDateTime": "End Date / Time", "createdDate": "Creation Date", "owner": "Owner"}, "STATE": {"new": "New", "pending": "Pending", "alt_pending": "New", "inProgress": "In Progress", "in_progress": "In Progress", "alt_in_progress": "Started", "finished": "Finished", "alt_finished": "Redeemed", "expired": "Expired"}, "STATUS": {"active": "Active", "inactive": "Inactive"}, "REWARD_STATUS": {"not_started": "New", "awarded": " Received promotion", "started": "Used promotion", "expired": "Expired", "redeemed": "Redeemed", "finished": "Finished", "redeemable_expired": "Redeemable expired", "pending": "Pending", "confirmed": "Confirmed", "revoked": "Revoked"}, "titlePromoDetails": "Promotion details", "titleCreatePromotion": "Create Promotion", "titleEditPromotion": "Manage Promotion", "titleViewPromotion": "View Promotion", "titleClonePromotion": "Clone Promotion", "titlePromosDisabled": "Promotions Disabled", "freebet": "Free Bets", "freebetSimple": "Simple Free Bets", "rebate": "Rebates", "virtualMoney": "Virtual Money", "bonusCoin": "Bonus Coins", "btnCreatePromo": "Create Promotion", "btnCreatePromoNew": "Create Promotion (new version)", "btnCreateSimpleFreeBet": "Create Simple Free Bet", "activatePromo": "Activate", "deactivatePromo": "Deactivate", "clonePromo": "<PERSON><PERSON>", "activatedNotification": "Promotion \"{{title}}\" was successfully activated!", "deactivatedNotification": "Promotion \"{{title}}\" was successfully deactivated!", "merchantPromotionsDisabled": "Promotions are not available for you, as your merchant doesn’t support promotions. Please contact customer support for more details.", "unableToSelectMerchant": "Selected Merchant is not allowed to have Promotion. Please contact customer support to enable \"Internal Promotions\"", "error": "Error"}, "MANAGEKEYS": {"Label": "", "accessKey": "Access key", "add": "Add new key", "confirmDelete": "Do you really want to delete key?", "processedWithSelectedKey": "Proceed with selected key", "selectKey": "Select key", "submit": "Add", "title": "Key management"}, "SETTINGS": {"title": "Settings", "timezone": "Timezone", "pageSize": "Page size", "theme": "Choose client theme", "dateFormat": "Choose date format", "timeFormat": "Choose time format", "currencyFormat": "Choose currency format", "currencyFormatDefault": "Use system's default", "reset": "Reset to default", "apply": "Apply", "decline": "Cancel", "pleaseProvideAappToken": "Please provide app token", "changeTwofa": "Change 2FA Methods", "enabledTwofaTypes": "Enabled Two-Factor Types", "btnAddNewTwofaType": "Add New Type", "defaultTwofaType": "Default Type", "markAsDefaultTwofaType": "<PERSON> as <PERSON><PERSON><PERSON>", "successTwofaMarkedAsDefault": "Auth type \"{{ name }}\" was marked as default", "successTwofaTypeAdded": "Auth type \"{{ name }}\" was added successfully", "notificationSuccess": "App settings saved"}, "SIDEBAR": {}, "COMPONENTS": {"NOTIFICATIONS": {"titleSuccess": "Success!", "status_inactive": "Status was successfully set to inactive", "status_active": "Status was successfully set to active"}, "WIZARD": {"btnPrev": "Previous", "btnNext": "Next", "btnSubmit": "Submit"}, "GRID": {"EMPTY_LIST": "No items found", "TOTAL": "Total", "COLLAPSE": "Collapse", "RELOAD": "Reload", "LOADING": "Loading...", "RESULT_DOANLOAD_CSV": "Download CSV", "SM_RESULT_DOWNLOAD": "Download SM Results", "RESULT_PRINT": "Print current page", "RESULT_EXPORT": "Export current page", "RESULT_EXPORT_CSV": "Export CSV", "RESULT_OPEN_NEW_TAB": "Open in new tab", "RESULT_EXPORT_FILENAME": "Export {title} {date} (Page {page})", "GRID_OPEN_DETAILS_POPUP": "View", "GRID_TOGGLE_FILTER": "Filter", "GRID_COLUMNS_VISIBILITY": "Columns visibility", "GRID_ROW_ACTIONS": "Actions", "ITEMS_FOUND": "items found"}, "PAGINATION": {"one_item_found": "item found", "many_items_found": "items found", "next": "Next page", "previous": "Previous page"}, "MULTISELECT": {"multiple": "Multiple values", "search": "Search"}, "MENU_SELECT": {"clear": "Clear", "cancel": "Cancel", "apply": "Apply", "search": "Search"}, "COLUMNS_MANAGEMENT": {"tooltip": "Columns management", "menuTitle": "Show columns"}, "DATERANGE": {"today": "Today", "yesterday": "Yesterday", "last3Days": "Last 3 days", "last7Days": "Last 7 days", "thisMonth": "This month", "prevMonth": "Last month", "customPeriod": "Custom period"}, "DATE_RANGE": {"today": "Today", "yesterday": "Yesterday", "last3Days": "Last 3 days", "last7Days": "Last 7 days", "last14Days": "Last 14 days", "last30Days": "Last 30 days", "last90Days": "Last 90 days", "next7Days": "Next 7 days", "next14Days": "Next 14 days", "next30Days": "Next 30 days", "next90Days": "Next 90 days", "thisMonth": "This month", "prevMonth": "Previous month", "lastMonth": "Last month", "nextMonth": "Next month", "monthToDate": "Month to date", "customPeriod": "Custom period", "clear": "Clear", "cancel": "Cancel", "apply": "Apply"}, "DATE_PICKER": {"clear": "Clear", "cancel": "Cancel", "apply": "Apply"}, "GAMES_SELECT_MANAGER": {"chooseTheGames": "Select the games to change", "view_all": "View all", "view_selected": "View selected", "selectedGamesTitle": "Selected Games", "availableGamesTitle": "Games", "availableLabelsTitle": "Labels", "searchGamesPlaceholder": "Search by game name, code or label", "searchLabelsPlaceholder": "Search by label name", "btnAddGames": "Add", "btnRemoveGames": "Remove", "btnIntersect": "Intersect", "selectAll": "Select All", "inlineGamesCount": "{{count}} games", "countGamesInIntersection": "{{count}} games in intersection", "countGamesInSelectedLabels": "{{count}} games in selected labels", "btnAddIntersection": "Add Intersection ({{count}} games)", "gamesNotFound": "Games Not Found", "noGamesToShow": "That's all folks! No more games to show.", "game": "Game", "itemNotAvailable": "Item {{ id }} is not available anymore", "emptySelectedGames": "Add games to this category using search", "PREVIEW": {"title": "Games in Category. Preview", "subtitle": "Games in Category {{count}}", "btnCancel": "Cancel", "btnApply": "Apply"}}, "CONDITIONS": {"allRules": "All of the following rules", "anyRules": "Any of the following rules", "clearAll": "Clear All", "addCondition": "Add Condition", "addGroup": "Add Group", "placeholderValueField": "Value Field...", "placeholderOperator": "Operator...", "placeholderValue": "Value"}, "BULK_ACTIONS": {"messageConfirm": "Do you really want to make the action?", "messageLess": "Only {{availableRowsNumber}} of {{checkedRowsNumber}} rows are available to make this action. Do you really want to continue?", "messageData": "There are no available data to make this action.", "btnYes": "Yes", "btnNo": "No", "btnOk": "Ok"}, "BO_SELECT": {"placeholderSearch": "Search item", "placeholder": "Select item"}, "WIDGET": {"andMore": "and {{number}} more"}}, "GAME_CATEGORIES": {"myCategories": "My Categories", "btnNew": "New", "btnNewCategory": "New Category", "btnPreview": "Preview", "btnSave": "Save", "textEmptyCategories": "Nothing to show yet.", "textPleaseClickNew": "To start building your lobby content, please click \"New Category\"", "categoryName": "Category Name", "gamesInCategory": "Games in Category", "emptySelectedGames": "Add games to this category using search", "emptySelectedGamesAlter": "No selected games", "deleteCategoryConfirm": "Do you really want to delete Category?", "selectLanguage": "Select language", "chooseIcon": "Choose icon (SVG)", "NOTIFICATIONS": {"created": "Category \"{{title}}\" successfully created!", "saved": "Category \"{{title}}\" successfully saved!", "deleted": "Category \"{{title}}\" successfully deleted!", "removeTab": "Do you really want to remove this tab?"}}, "HINTS": {"GAME_CATEGORIES": {"buildContentByManageCategories": "Build your lobby content by managing categories and adding games.\nYou can add individual games to a category. Or, add labels to have the category automatically show all games in the label.", "inheritedCategories": "<b>Note:</b> The list of categories is inherited from parent"}, "btnGotIt": "Got it", "btnCloseHint": "Close"}, "VALIDATION": {"dateGreaterThan": "Selected date is older than {{value}}", "dateGreaterThanNow": "Selected date should be in future", "domainHttp": "Url can not have a protocol part", "domainUrlParts": "Url must have 2 or more levels", "JSON": "Invalid JSON data: {{error}}", "JSONinvalid": "Invalid JSON data", "invalidCreditCard": "Is invalid credit card number", "invalidEmailAddress": "Invalid email address", "invalidPassword": "Invalid password. Password must be at least 8 characters long, and contains at least one letter, one uppercase letter and one digit. Only latin characters are allowed", "invalidDomain": "Invalid Domain name", "invalidIPv4Address": "Invalid IPv4 address", "invalidIPv4AddressMask": "Invalid IPv4 address or mask", "invalidPhoneNumberMask": "Invalid phone number. Number must contain contain only 0..9 digits with optional sign at the beginning (e.g. +1234567890 or 123567890)", "invalidDigitsOnly": "Invalid value. Only digits are allowed", "invalidNumbersOnly": "Invalid value. Only numbers are allowed", "invalidIssueId": "Invalid issue id", "min": "Minimum value is {{min}}", "max": "Maximum value is {{max}}", "minLength": "Minimum length is {{min}}", "maxLength": "Maximum length is {{max}}", "minLengthArray": "Minimum amount of items is {{value}}", "notEquals": "Incorrect value", "notEqualsPassword": "Provided password is not acceptable", "notEqualsString": "Value {{value}} is not acceptable", "passwordNotEquals": "Password doesn't match", "passwordMinLength": "Password is shorter than required", "passwordContainDigit": "Password should contain at least {{value}} digit", "passwordContainLowercase": "Password should contain at least {{value}} lowercase letter", "passwordContainUppercase": "Password should contain at least {{value}} uppercase letter", "passwordNotMatch": "The new password and the new password confirmation don't match", "required": "Required", "coeffsRequired": "Coefficients are mandatory", "invalidLatinCharsDigitsSymbols": "Value should contain only these characters: 0..9 a..z A..Z _ - : .", "startAndEndDate": "Start date should be older than end date", "urlIsNotCorrect": "Url is not acceptable", "fileFormatNotSupported": "File format not supported", "spacesAreNotAllowed": "Spaces are not allowed", "invalidColorHexFormat": "Color must be in HEX format (#000000 or #000)", "invalidCustomerId": "Length should be between 6 and 30 symbols. Spaces are not allowed.", "validateFirstMaxValueIfSecondSetWithValueDynamic": "Field {{firstDisplayName}} should be lower than {{value}} or equal due to {{secondControlValue}} in {{secondDisplayName}}", "validateFirstMaxValueIfSecondSetWithValue": "Field Expiration should be lower than {{value}} or equal due to daily in expiration period type", "invalidStakeAll": "Invalid format. Only positive numbers >= 0.01 separated by ', ' are allowed", "invalidLiveStakeAll": "Invalid format. Only positive numbers >= 0.01 separated by ' ; ' are allowed", "invalidStakeDef": "Stake Def should be from Coin Bets values", "limitsCanNotCloneThemselves": "Game limits can't clone themselves", "valuesCanNotBeDuplicated": "Values can't be duplicated", "invalidFormat": "Invalid format. Name cannot start with \"http://\" or \"https://\"", "aamsCodeError": "AAMS code in settings is required for Italian regulation. Only [0-9] numbers are allowed.", "valuesAreNotTheSame": "Values are not the same", "positiveNumbers": "Value should be positive number", "stakeAllOutOfRange": "Coin Bets values should be from {{ range }} range", "fractionsNumbersLength": "Max 2 decimal digits are allowed", "minLowerThanMax": "min should be < max", "lowerThan": "Should be lower than {{value}}", "greaterThan": "Should be greater than {{value}}", "schemaDefinitionIsIncorrect": "Format of schema definition is incorrect", "greaterOrEqual": "{{min}} should be <= {{max}}", "noDecimals": "No decimals are allowed", "valueShouldBeUnique": "Value should be unique", "positiveInteger": "Value should be a positive integer"}, "BACKEND_ERRORS": {"STATUS_400__CODE_101": "Not a brand", "STATUS_400__CODE_103": "Limits incorrect", "STATUS_400__CODE_107": "Invalid game payment: {{payment}}", "STATUS_400__CODE_108": "Max capacity is reached", "STATUS_400__CODE_109": "Operation is permitted only for brands or merchants", "STATUS_400__CODE_151": "Too many items for group action", "STATUS_400__CODE_152": "Incorrect action query", "STATUS_400__CODE_199": "Email already used", "STATUS_400__CODE_207": "Provider user", "STATUS_400__CODE_214": "Game doesn't belong to game group", "STATUS_400__CODE_217": "Bad query for updating status", "STATUS_400__CODE_218": "Bad query for updating agent", "STATUS_400__CODE_226": "Bad query for updating token status", "STATUS_400__CODE_228": "New password should be different from a previous one", "STATUS_400__CODE_229": "The '{{permission}}' Permission not exist in list", "STATUS_400__CODE_303": "Bad query for updating status", "STATUS_400__CODE_306": "Game is suspended", "STATUS_400__CODE_307": "Game category not empty", "STATUS_400__CODE_308": "Parameter royalties is mandatory for adding game to entity", "STATUS_400__CODE_311": "Game provider is suspended", "STATUS_400__CODE_320": "Start game token error", "STATUS_400__CODE_321": "Start game token is expired", "STATUS_400__CODE_322": "Game token error", "STATUS_400__CODE_323": "Game token expired", "STATUS_400__CODE_328": "Bad request for terminal updating", "STATUS_400__CODE_329": "Bad query for getting terminals", "STATUS_400__CODE_330": "Bad request for lobby updating", "STATUS_400__CODE_331": "Lobby could not be deleted cos it have assigned terminals: {{terminals}}", "STATUS_400__CODE_340": "No receivers found for notification", "STATUS_400__CODE_341": "Notification not found", "STATUS_400__CODE_40": "Validation error: {{messages}}", "STATUS_400__CODE_403": "Sort by is not valid", "STATUS_400__CODE_41": "The Email Address is in an invalid format", "STATUS_400__CODE_42": "Player code should contain from 6 to 30 characters", "STATUS_400__CODE_43": "Provided password is not valid", "STATUS_400__CODE_501": "Merchant type is not supported", "STATUS_400__CODE_503": "Merchant brand doesn't support this operation", "STATUS_400__CODE_504": "Not a merchant brand", "STATUS_400__CODE_61": "Parent not found", "STATUS_400__CODE_62": "One of the parents is suspended", "STATUS_400__CODE_622": "Bad query for updating Role", "STATUS_400__CODE_625": "Add Role to User failed {{reason}}", "STATUS_400__CODE_64": "Entity is not empty!", "STATUS_400__CODE_669": "Label record not created", "STATUS_400__CODE_670": "Payment method not found", "STATUS_400__CODE_671": "Payment method type not found", "STATUS_400__CODE_672": "Could not find transaction to perform operation.", "STATUS_400__CODE_674": "Malformed JSON : {{reason}}", "STATUS_400__CODE_675": "Invalid payment action attribute: {{attribute}}", "STATUS_400__CODE_676": "Invalid payment action type: {{action}}", "STATUS_400__CODE_677": "Negative payment action value", "STATUS_400__CODE_678": "Payment action list is empty", "STATUS_400__CODE_685": "Insufficient free bets balance", "STATUS_400__CODE_686": "Invalid free bet", "STATUS_400__CODE_703": "IP address {{ip}} cannot be resolved", "STATUS_400__CODE_705": "Fail to obtain external game url", "STATUS_400__CODE_709": "Player has no such promo", "STATUS_400__CODE_710": "Can't execute operation for promo with its type", "STATUS_400__CODE_711": "Promo is not in a valid state", "STATUS_400__CODE_712": "Player is suspended", "STATUS_400__CODE_713": "Password not confirm", "STATUS_400__CODE_714": "Password should be different from username", "STATUS_400__CODE_715": "It is forbidden to start game from unauthorized site", "STATUS_400__CODE_716": "JPN bad request error", "STATUS_400__CODE_81": "You cannot remove default country", "STATUS_400__CODE_82": "Country not in list", "STATUS_400__CODE_83": "Country not exist in parent", "STATUS_400__CODE_86": "You cannot remove default currency", "STATUS_400__CODE_87": "Currency not in list", "STATUS_400__CODE_88": "Currency not exist in parent", "STATUS_400__CODE_89": "Currency not exist", "STATUS_400__CODE_90": "Amount is negative", "STATUS_400__CODE_91": "Insufficient balance", "STATUS_400__CODE_92": "Insufficient entity balance", "STATUS_400__CODE_94": "You cannot remove default language", "STATUS_400__CODE_95": "Language not in list", "STATUS_400__CODE_96": "Language not exist in parent", "STATUS_400__CODE_97": "Countries is not array", "STATUS_400__CODE_98": "Currencies is not array", "STATUS_400__CODE_99": "Languages is not array", "STATUS_401__CODE_10": "Access Token is missing", "STATUS_401__CODE_201": "Password does not match", "STATUS_401__CODE_202": "User or Password does not match", "STATUS_401__CODE_203": "Password incorrect", "STATUS_401__CODE_204": "Access token error", "STATUS_401__CODE_205": "Access Token is expired", "STATUS_401__CODE_208": "Terminal token error", "STATUS_401__CODE_209": "Provider secret incorrect", "STATUS_401__CODE_221": "Reset password link is expired", "STATUS_401__CODE_222": "Reset password already complete or something go wrong", "STATUS_401__CODE_223": "Player created without password", "STATUS_401__CODE_224": "Password has not changed", "STATUS_401__CODE_225": "Player info has not changed", "STATUS_401__CODE_716": "Provided auth code is incorrect", "STATUS_401__CODE_717": "Two factor auth token is expired", "STATUS_401__CODE_718": "Two factor auth token error", "STATUS_401__CODE_719": "An error occurred when sending sms. Check logs", "STATUS_401__CODE_720": "An error occurred when sending email. Check logs", "STATUS_401__CODE_721": "Selected auth type is not allowed", "STATUS_401__CODE_722": "Two factor auth is not configured", "STATUS_401__CODE_723": "Two factor auth code was not generated or expired", "STATUS_403__CODE_206": "Forbidden", "STATUS_403__CODE_219": "Can not update archived promo", "STATUS_403__CODE_50": "Not master entity", "STATUS_403__CODE_626": "You cannot manage role", "STATUS_403__CODE_701": "Country {{country}} of IP:{{ip}} is restricted", "STATUS_403__CODE_702": "Currency {{currency}} is restricted for IP:{{ip}}", "STATUS_403__CODE_706": "Can not update promo that is not pending", "STATUS_403__CODE_707": "<PERSON> has exceeded max number of test players", "STATUS_404__CODE_102": "Player not found", "STATUS_404__CODE_104": "Limits for the currency not found", "STATUS_404__CODE_105": "Session not found", "STATUS_404__CODE_106": "Session already finished", "STATUS_404__CODE_110": "Whitelist not found", "STATUS_404__CODE_202": "User does not exist", "STATUS_404__CODE_211": "Game group is not found", "STATUS_404__CODE_213": "Game is not available for entity", "STATUS_404__CODE_215": "No record for this Domain in agents", "STATUS_404__CODE_227": "Site token does not exist", "STATUS_404__CODE_300": "Game not found", "STATUS_404__CODE_304": "Game category is not found", "STATUS_404__CODE_312": "Game provider not found", "STATUS_404__CODE_324": "Lobby is not found", "STATUS_404__CODE_327": "Terminal is not found", "STATUS_404__CODE_343": "Site not found", "STATUS_404__CODE_502": "Merchant not found", "STATUS_404__CODE_51": "Could not find entity", "STATUS_404__CODE_600": "Transaction not found", "STATUS_404__CODE_623": "Role not exist", "STATUS_404__CODE_680": "Promotion not found", "STATUS_404__CODE_681": "<PERSON><PERSON><PERSON> not found", "STATUS_404__CODE_683": "Game history details not found", "STATUS_404__CODE_684": "Referenced {{item}} is not found", "STATUS_404__CODE_715": "Two Factor Authentication is not set for user", "STATUS_404__CODE_80": "Country not found", "STATUS_404__CODE_85": "Currency not found", "STATUS_404__CODE_93": "Language not found", "STATUS_409__CODE_100": "Player already exist", "STATUS_409__CODE_111": "Whitelist already exists. You can patch it", "STATUS_409__CODE_200": "User already exist", "STATUS_409__CODE_210": "Game group already exists", "STATUS_409__CODE_212": "Game already exists in game group", "STATUS_409__CODE_301": "Game already exists", "STATUS_409__CODE_302": "Failed to delete entity game with child entity games. Need force flag", "STATUS_409__CODE_305": "Game category already exists", "STATUS_409__CODE_310": "Game provider already exists", "STATUS_409__CODE_325": "Lobby already exists", "STATUS_409__CODE_326": "Terminal already exists", "STATUS_409__CODE_342": "Site already exists", "STATUS_409__CODE_500": "Merchant already exists", "STATUS_409__CODE_60": "Entity already exist", "STATUS_409__CODE_624": "Failed to delete role with linked users. Need force flag", "STATUS_409__CODE_63": "Entity is being edited now!", "STATUS_409__CODE_682": "Failed to delete item that is referenced from other table. Need force flag", "STATUS_500__CODE_1": "Internal server error", "STATUS_500__CODE_216": "Bad BrandId from list of agents or Domain is not unique", "STATUS_500__CODE_220": "Bad BrandId or code is not unique", "STATUS_500__CODE_405": "Missing currency exchange rates", "STATUS_500__CODE_505": "Merchant is not configured properly", "STATUS_500__CODE_621": "Role record not created", "STATUS_500__CODE_673": "Not yet ready to rollback transaction. Repeat later.", "STATUS_500__CODE_700": "Ip location lookup error: {{message}}", "STATUS_500__CODE_717": "JPN internal server error", "STATUS_502__CODE_506": "Merchant internal error {{reason}}", "STATUS_502__CODE_507": "Error during integration with merchant {{reason}}", "STATUS_502__CODE_672": "Can't transfer out money to external wallet", "code": "Code: ", "status": "Status: ", "error_code": "Error Code: "}, "WEEK": {"Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday"}, "ACTIVITY_LOG": {"GRID": {"dateTime": "Date/Time", "activity": "Activity", "includeActivity": "Include activity", "excludedActivity": "Exclude activity", "history": "History", "initiatorType": "Initiator Type", "statusCode": "Status Code", "login": "<PERSON><PERSON>", "initiatorService": "Initiator service", "initiatorIssueId": "Issue ID", "ip": "IP", "system": "System", "showHistoryTitle": "View history", "entity": "Entity", "eventName": "Event name", "summary": "Summary", "path": "Path", "pathInclude": "Include path (Separated by \",\")", "pathExclude": "Exclude path (Separated by \",\")", "operation": "Operation", "method": "Method", "methodInclude": "Include method", "methodExclude": "Exclude method", "includeSubEntities": "Include sub entities"}, "FILTER": {"customer": "Player", "user": "User", "system": "System", "backoffice": "Management API", "games": "GameProvider API", "operator": "Operator API", "report": "Report API", "player": "Player API", "terminal": "Terminal API", "path": "Reseller / Operator", "includeSubEntities": "Include Sub-Entities", "boMapi": "BO (Management API)", "METHOD": {"get": "GET", "post": "POST", "patch": "PATCH", "put": "PUT", "delete": "DELETE", "cron": "CRON", "service": "SERVICE"}, "include": "Include", "dont_include": "Don't include"}, "VIEW_HISTORY_MODAL": {"detailedHistory": "Detailed history", "parameters": "Parameters", "result": "Result", "copyToClipboard": "Copy to clipboard"}, "CSV": {"method": "Method", "eventName": "Event name", "date-time": "Date/Time (GMT {{zone}})", "entity": "Entity", "summary": "Summary", "history": "History", "initiatorType": "Initiator Type", "initiatorName": "<PERSON><PERSON>", "initiatorServiceName": "Initiator service", "ip": "Ip", "userAgent": "System", "actions": "Actions", "path": "Path", "login": "<PERSON><PERSON>", "system": "System"}}, "USERS": {"editUser": "Edit User", "createUser": "Create User", "btnClose": "Close", "btnSave": "Save Changes", "GRID": {"entityCode": "Entity code", "entityPath": "Entity path", "fullName": "Name", "username": "Username", "userId": "User ID", "role": "Role", "email": "E-mail", "created": "Created", "modified": "Modified", "lastLogin": "Last login", "status": "Status", "actions": "Actions", "password": "Password", "repeatPassword": "Repeat password", "edit": "Edit item", "type": "Type"}, "FILTER": {"firstName": "First name", "lastName": "Last name", "system": "System", "active": "Active", "inactive": "Inactive", "locked_by_auth": "Locked", "emailPlaceholder": "<EMAIL>", "boUserType": "BackOffice", "operatorApiUserType": "Operator API", "studioUserType": "Live Studio"}, "CREATE": {"userFormTextFieldSize": "Min length {{min}}, max length 50"}, "FORM": {"general": "General", "name": "First Name", "lastName": "Last Name", "username": "Username", "email": "Email", "password": "Password", "btnReset": "Reset password", "status": "Status", "active": "Active", "inactive": "Inactive", "role": "Role", "additionalRoles": "Additional roles"}, "ROLES": {"list": "Roles List", "btnCreate": "Create Role", "role": "Role", "actions": "Actions", "notificationRemoved": "Role was successfully removed!", "notificationEdited": "Role was successfully edited!", "notificationAdded": "Role was successfully added!", "edit": "Edit role", "add": "Add role", "view": "View role", "delete": "Delete role", "MODAL": {"title": "Title", "description": "Description", "shared": "Shared role", "btnClose": "Close", "btnSave": "Save Changes", "confirmMessage": "Do you really want to delete this role?", "messageLinkedRole": "This role is linked with some users!", "messageFlag": "If you want to delete it please add force flag.", "labelAddFlag": "Add force flag", "btnYes": "Yes", "btnNo": "No"}}, "CSV": {"entityPath": "Entity path", "entityCode": "Entity code", "name": "Name", "username": "Username", "roles": "Role", "email": "E-mail", "createdAt": "Created (GMT {{zone}})", "updatedAt": "Modified (GMT {{zone}})", "lastLogin": "Last login (GMT {{zone}})", "userType": "Type", "status": "Status"}}, "BUSINESS_STRUCTURE": {"btnExpand": "Expand All", "searchByDomain": "Search by domain", "title": "Title", "code": "Code", "key": "Key", "regional": "Regional", "currency": "<PERSON><PERSON><PERSON><PERSON>", "country": "Country", "language": "Language", "gameGroup": "Game group", "jurisdiction": "Juris<PERSON>", "status": "Status", "actions": "Actions", "notificationCreated": "\"{{name}}\" was created successfully", "notificationEdited": "\"{{name}}\" was updated successfully", "editRegionalEntitySettings": "Edit regional {{title}} settings", "MODAL_GENERAL": {"wellDone": "Well done!", "clickHere": "Click here", "initiateSetup": "{{clickHere}} to initiate setup of your new entity {{entityTitle}}", "settings": "Settings", "hint": "Hint: You can continue entity setup later by clicking {{settings}} located in Actions area", "btnCancel": "Cancel", "btnSave": "Save", "btnCreate": "Create", "btnClose": "Close", "title": "Title", "type": "Type", "optionEmpty": "Please select...", "reseller": "Reseller", "operatorWallet": "Operator (wallet)", "operatorSeamless": "Operator (seamless)", "name": "Name", "status": "Status", "active": "Active", "inactive": "Inactive", "password": "Password", "serverURL": "Server URL", "code": "Code", "promotions": "Internal Promotions", "enabled": "Enabled", "disabled": "Disabled", "description": "Description", "placeholderTitle": "Entity Title", "placeholderName": "Entity Name", "playerPrefix": "Player Prefix", "placeholderCode": "Entity Title", "storePlayerInfo": "Store Player Info", "supportedMerchantTypes": "Supported merchant types", "isPlayerPasswordChangeEnabled": "Force player change password", "webSiteUrl": "Website URL"}, "MODAL_STATUS": {"setStatus": "Set {{statusTitle}} status", "changeStatus": "Do you really want to change status to <b>{{statusTitle}}</b>?"}, "WIDGETS": {"andMore": "and {{number}} more", "manageBalance": "Manage Balance", "loading": "Loading...", "addCountry": "Add Country", "addLanguage": "Add Language", "active": "Active", "inactive": "Inactive", "maintenance": "Maintenance", "blocked": "Blocked", "blockedByAdmin": "Blocked by <PERSON><PERSON>", "settings": "Settings", "addChild": "Add Child", "move": "Move", "addCascade": "Add Games Cascade", "showGameLimits": "Show Game Limits", "info": "Info", "labels": "Labels", "test": "Test"}, "MOVE_ENTITY": {"title": "Move Entity", "description": "Choose Entity/Brand/Merchant to move and new parent Entity", "entityBrandMerchantToMovePlaceholder": "Entity/Brand/Merchant to move", "newParentEntityPlaceholder": "New parent Entity", "move": "Move", "successNotification": "Entity \"{{entityPath}}\" successfully moved to \"{{newParentPath}}\"", "errorCode": "Error Code"}}, "LOBBY": {"selectOperator": "Select operator", "selectEntity": "Select entity", "create": "New Lobby", "buildInProgress": "Build in progress...", "messageRemove": "Do you really want to remove lobby?", "notificationCreate": "Lobby \"{{title}}\" successfully created", "notificationUpdate": "<PERSON>bby \"{{title}}\" successfully edited", "notificationDelete": "Lobby \"{{title}}\" successfully deleted", "copy": "Create copy", "edit": "Edit", "delete": "Delete", "copyTitle": "{{title}} - copy", "android": "Native Android (APK)", "mac": "Desktop for Mac", "win32": "Desktop for Win32", "win64": "Desktop for Win64", "installer": "Installer for Windows", "browser": "Web App (browser)", "open": "Open", "copyUrl": "Copy URL", "newWindow": "New window", "MENU_ITEMS": {"menuItems": "Menu items", "categories": "Categories", "options": "Options", "widgets": "Widgets", "newItem": "New menu item", "preview": "Preview", "empty": "Game category is empty", "notSelected": "Game category is not selected", "addChild": "Add Child", "showCommissionFilter": "Show commission filter", "useGridLayout": "Use grid layout", "WIDGET": {"header": "Header widget", "footer": "Footer widget", "FORM": {"tag": "Widget"}}, "overlayUrl": "Overlay URL"}, "FORM": {"settings": "Settings", "slug": "URL slug", "title": "Title", "description": "Description", "template": "Template", "templateSetup": "Template Setup", "templateList": "Template list", "active": "Active", "activate": "Activate", "save": "Save", "back": "Back", "gameLaunch": "Game launch", "gameLaunchDesktop": "Browser on desktop", "gameLaunchMobile": "Browser on mobile device", "gameLaunchOption": {"modal": "Modal", "same-window": "Same window", "new-window": "New window (unlimited)", "new-three-windows": "New window (max 3 window)"}, "GAME_LAUNCH": {"WINDOW_SIZE": {"title": "Window size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "value": "Value", "type": "Type", "typeOption": {"fixed": "Pixels", "percentage": "Percentage", "ratio": "<PERSON><PERSON>"}}}}, "THEMES": {"cashierURL": "Cashier URL", "defaultLanguage": "Default Language", "loginURL": "Login URL", "logoImg": "Logo image (PNG)", "selectLogo": "Select logo", "selectIcon": "Select icon", "iconImg": "Icon image (PNG)", "iconUrl": "URL", "iconCaption": "Icon caption", "chooseFile": "Choose file", "btnAdd": "Add", "isLogoOnLogin": "Show logo image on login page", "showUnfinishedGames": "Show unfinished games on login/logout page", "customCss": "Custom CSS", "exportSettings": "Export settings JSON", "importSettings": "Import settings JSON", "jsonLoaded": "JSON is loaded successfully and applied to the theme", "jsonEmpty": "JSON is loaded successfully but there is no CSS inside", "syntaxJsonError": "Unexpected end of JSON input!", "COLORS": {"bgPrimary": "Primary background color", "bgExtra": "Additional background color", "bgHighlight": "Highlighted element background color", "fontPrimary": "Primary font color", "fontExtra": "Additional font color", "fontHighlight": "Highlighted element font color", "borderColor": "Border color", "fontHoverColor": "Font/icons hover color", "errorColor": "Error message color", "bgLoginDialogColor": "Login dialog background color", "bgLoginImage": "Login background image (PNG)", "bgHomeImage": "Home background image (PNG)", "bgHeader": "Header background color", "bgPanel": "Panel background color", "bgSidebar": "Sidebar background color", "bgModal": "Modal dialog background color", "bgButtonAdditional": "Additional button background color", "bgListItemStateHover": "List item hover background color", "bgListItemStateActive": "List item active background color", "iconViewToggleColor": "View toggle button color", "bgTriangleColor": "Sidebar triangle", "shadowBalance": "Balance shadow", "bgScrollBar": "Scroll bar background color", "bgScrollThumb": "Scroll thumb background color", "dialogBackgroundColor": "Dialog background color", "inputsBorderColor": "Inputs border color", "backgroundImage": "Background image (PNG)", "backgroundColor": "Background color", "backgroundColorMobile": "Background color (mobile)", "textColor": "Text color", "hoverTextColor": "Hover text color", "hoverBackgroundColor": "Hover background color", "defaultImage": "Default image", "scoreboardBackgroundColor": "Scoreboard background color (mobile portrait orientation)", "headerTextColor": "Header text color", "timeColor": "Time color", "balanceLabelTextColor": "'Balance' label text color", "balanceValuetextColor": "'Balance' value text color", "balanceLabelTextColorMobile": "'Balance' label text color (mobile)", "balanceValueTextColorMobile": "'Balance' value text color (mobile)", "scrollTrackBackgroundColor": "Scroll track background color", "scrollThumbBackgroundColor": "Scroll thumb background color", "hoverBorderColor": "Hover border color", "activeBorderColor": "Active border color", "closeColor": "Close button color", "closeHoverColor": "Close button hover color", "iconTextColor": "Icon text color", "iconTextHoverColor": "Icon text hover color", "minimisedGameCaptionBg": "Minimised game caption background color", "minimisedGameCaptionColor": "Minimised game caption text color", "minimisedGameBorderColor": "Minimised game border color", "nextButtonHighlightBg": "'Next category' button highlighted background color", "itemHighlightedBgMobile": "Highlighted element background color (mobile)", "color": "Color", "hoverColor": "Hover color", "titleColor": "Title color", "itemTextColor": "Item text color", "limitsPreviewBg": "Limits preview background color", "limitsPreviewColor": "Limits preview text color", "limitsOptionsBg": "Limits options background color", "limitsOptionsItemBg": "Limits options item background color", "limitsOptionsItemColor": "Limits options item text color", "limitsOptionsItemHighlightColor": "Limits options highlighted item text color", "limitsOptionsHeaderColor": "Limits options header text color", "gameItemBg": "Game item background color", "gameItemColor": "Game item text color", "gameItemHighlightedColor": "Game item highlighted text color", "menuBtnColor": "Menu button color", "gameThumbBgImage": "Game thumb background image", "highlightColor": "Highlight color", "categoriesBg": "Categories background color", "thumbBg": "Thumb background color", "bottomBorderColor": "Bottom border color"}, "GROUPS": {"groupLogin": "Login page view", "groupButtonDefault": "Default button view", "groupButtonHighlight": "Highlighted button view", "groupFooter": "Footer view", "groupLanguageSelectBox": "Language select box view", "groupGameThumb": "Game thumb view", "groupLimits": "Limits view", "groupLiveTimer": "Live timer view", "groupPage": "Page view", "groupModal": "Modal view", "groupModalGame": "Modal game view", "groupUnfinished": "Unfinished games view", "groupHeader": "Header view", "groupCategories": "Categories view", "groupMobileMenu": "Mobile menu view", "groupWelcome": "Player welcome view", "groupMobile": "Mobile view"}, "LAYOUT": {"layoutType": "Thumbnails size", "large": "Large", "medium": "Medium", "small": "Small"}, "PRELOADER": {"showPreloader": "Show preloader", "selectLogo": "Select preloader logo", "preloaderLogo": "Preloader logo (PNG,SVG,JPG)", "preloaderAnimation": "Preloader animation", "disabled": "Disabled", "fadeIn": "Fade in", "scaleIn": "Scale in", "slideLeft": "Slide left", "slideRight": "Slide right", "backgroundColor": "Preloader background color"}, "REFRESH_LIVE": {"title": "Refresh interval for Live tables (sec)", "value1": "15", "value2": "30", "value3": "45", "value4": "60"}, "PLAYER_INACTIVITY": {"title": "Player inactivity timeout", "value0": "Disabled", "value4": "10 minutes", "value5": "15 minutes", "value6": "30 minutes", "value7": "1 hour", "value8": "2 hours"}, "disableLogout": "Disable logout option", "disableCloseGame": "Disable close game", "numberOfPlayers_show": "Show number of players", "numberOfPlayers_games": "Number of players per game/table", "gameName": "Game name", "gameCode": "Game code"}}, "INTEGRATIONS": {"selectOperator": "Select operator", "pleaseSelectEntity": "Please, select entity!", "wallet": "Operator (Wallet)", "url": "URL", "walletDoc": "iWallet API Documentation", "seamlessDoc": "Seamless API Documentation", "seamless": "Operator (Seamless)", "merchantCode": "Merchant Code", "merchantPassword": "Merchant Password", "serverURL": "Server URL", "error": "Error", "tests": "Tests", "success": "Success", "failures": "Failures", "start": "Start", "end": "End", "duration": "Duration", "confirmMessage": "Do you really want to save settings?", "btnYes": "Yes", "btnNo": "No", "btnSave": "Save", "btnTest": "Test", "btnCancel": "Cancel", "btnEdit": "Edit", "notificationConfigSaved": "Settings applied successfully", "notificationCopy": "Selected content was copied to clipboard!", "lastIntegrationsTestsPassed": "Last integrations tests passed", "gameCode": "Game Code", "secondGameCode": "Second Game Code", "merchantType": "Merchant Type", "customId": "Player code", "currencyCode": "Currency code", "ticket": "Ticket", "json": "JSON", "loading": "Loading", "testsPassed": "All tests passed", "testsFailed": "Some tests are failed", "testId": "Test Id", "merchantName": "Merchant Name", "createdAt": "Created At", "details": "Details", "time": "Time", "request": "Request", "response": "Response", "statusCode": "Status Code", "requestBody": "Request Body", "responseBody": "Response Body", "responseTime": "Response Time", "collapse": "Collapse", "expanded": "Expanded", "merchantSettings": "Merchant settings", "testExecutionParameters": "Test execution parameters (optional)", "ticketRequiredCase": "Ticket is required in case the \"get_ticket\" API endpoint was not implemented", "warningMessage": "Integration tests are available only for operators type BRAND or MERCHANT"}, "MARKETING_MATERIALS": {"btnBack": "Back to Marketing Materials", "seeAll": "See All", "seeMore": "See More", "GAME_DETAILS": {"btnBack": "Back to Marketing Materials", "btnGet": "Get", "btnMore": "Read More", "btnLess": "Read Less", "btnDownload": "Download", "rtp": "RTP", "languages": "Languages", "countries": "Countries", "currencies": "Currencies", "tags": "Tags", "slot": "Slot", "packageCurrent": "Current Package", "packageAll": "All Packages", "screenshots": "Screenshots", "overlay": "Overlay", "marketing": "Marketing", "files": "{{number}} files", "MODAL": {"getURL": "Click URL to start game for fun", "getToken": "Game token", "btnClose": "Close"}, "GROUPS": {"Characters": "Characters", "Fonts": "Fonts", "Game Logo": "Game logo", "Icons": "Icons", "Posters": "Posters", "Promo Video": "Promo video", "Sale Sheet": "Sale sheet", "Screenshot": "Screenshot", "Site Icon": "Site icon", "Source file": "Source file", "Symbols": "Symbols"}}}, "DOMAINS": {"title": "Domains Management", "addDomain": "Add domain", "addPool": "Add pool", "addDynamicPool": "Add dynamic pool", "editDomain": "Edit domain", "addStaticDomain": "Add static domain", "addDynamicDomain": "Add dynamic domain", "addLobbyDomain": "Add lobby domain", "addStaticPool": "Add static pool", "editStaticDomain": "Edit static domain", "editDynamicDomain": "Edit dynamic domain", "editLobbyDomain": "Edit lobby domain", "editStaticPool": "Edit static pool", "removeMessage": "Do you really want to remove domain?", "removePoolMessage": "Do you really want to remove pool?", "domainPools": "Domain Pools", "staticDomainPools": "Static Domain Pools", "dynamicDomainPools": "Dynamic Domain Pools", "staticDomains": "Static Domains", "lobbyDomains": "Lobby Domains", "dynamicDomains": "Dynamic Domains", "staticPoolDomains": "Static Domain Pools", "notificationCreated": "Domain \"{{domain}}\" was created successfully", "notificationPoolCreated": "Pool \"{{name}}\" was created successfully", "notificationModified": "Domain \"{{domain}}\" was modified successfully", "notificationRemoved": "Domain \"{{domain}}\" was removed successfully", "notificationPoolModified": "Pool \"{{name}}\" was modified successfully", "notificationPoolRemoved": "Pool \"{{name}}\" was removed successfully", "GRID": {"created": "Created", "domain": "Domain", "name": "Name", "type": "Type", "description": "Description", "expiryDate": "Expiry Date", "active": "Status", "activeEnabled": "Enabled", "activeDisabled": "Disabled", "activeEnable": "Enable", "activeDisable": "Disable", "editDomain": "Edit Domain", "editPool": "Edit Pool", "environment": "Environment", "gameServer": "Game server", "removeDomain": "Remove Domain", "removePool": "Remove Pool", "serverDescription": "Server description", "updated": "Updated", "status": "Status", "statusActive": "Active", "statusInactive": "Inactive", "typeStatic": "Game", "typeLobby": "Lobby", "typeLiveStreaming": "Live Streaming", "typeEhub": "EHub", "typeDynamic": "Dynamic", "poolType": "Pool Type", "staticDomainType": "Static Domain Type", "selectStaticDomainType": "Select Static Domain Type"}}, "PROXY": {"title": "Proxy Management", "addNew": "Add new", "addProxy": "Add new proxy", "editProxy": "Edit proxy", "removeProxy": "Remove proxy", "notificationCreated": "Proxy \"{{proxy}}\" was created successfully", "notificationModified": "Proxy \"{{proxy}}\" was modified successfully", "notificationRemoved": "Proxy \"{{proxy}}\" was removed successfully", "notificationProxyRemove": "Do you really want to remove proxy: \"{{proxy}}\"?", "GRID": {"created": "Created", "updated": "Updated", "url": "URL", "description": "Description"}}, "GRC_BLOCK": {"title": "Challenges managing", "create": "Create challenge", "notificationRemove": "Do you really want to remove challenge?", "notificationChallengeRemoved": "Challenge ID: {{challengeId}} successfully removed", "notificationCreated": "Challenge successfully created!", "notificationConfigSaved": "Configuration successfully saved!", "newChallengePlaceholder": "Put new challenge JSON here...", "saveConfig": "Save configuration", "endChallenge": "End challenge", "namePlaceholder": "Enter challenge name", "potAmountPlaceholder": "Enter pot amount", "targetBadgesPlaceholder": "Enter target badges", "logoUrlPlaceholder": "Enter logo URL", "GRID": {"id": "ID", "name": "Name", "state": "State", "potAmount": "Pot amount", "targetBadges": "Target badges", "logoUrl": "Logo URL", "startAt": "Start at", "finishAt": "Finish at", "actions": "Actions", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "EMAIL": {"emailManagement": "Email management", "titleStart": "Challenge start email", "titleEnd": "Challenge canceled email", "notificationEmailSendSuccess": "Email has been successfully sent!", "notificationEmailGetSuccess": "Email template has been successfully received!", "tournamentCanceled": "Tournament has been canceled", "FORM": {"name": "From", "nameEn": "Challenge name in English", "nameCh": "Challenge name in Chinese", "recipients": "Recipients (one per row)", "subject": "Subject", "template": "Email template", "btnGetEmail": "Get email template", "btnSendEmail": "Send email"}}, "DEFINITION": {"editDefinition": "Edit definition", "notificationDefinitionUpdated": "Definition successfully updated", "logoUrl": "Logo URL"}}, "GAME_SERVER": {"title": "Game server settings", "editForm": "Edit form", "createForm": "Create form", "createNew": "Create new", "editServer": "Edit server", "removeServer": "Remove server", "messageRemove": "Do you really want to remove game server config?", "notificationConfigSaved": "Configuration successfully saved!", "notificationConfigAdded": "Configuration successfully added!", "notificationConfigRemoved": "Configuration successfully removed!", "configPlaceholder": "Put new game server config JSON here...", "GRID": {"name": "Name", "description": "Description", "roundIdRange": "Round ID Range", "sessionIdRange": "Session ID Range", "createdAt": "Created", "modifiedAt": "Modified"}}, "BULK_ACTIONS": {"dynamicDomains": "Dynamic Domains", "staticDomains": "Static Domains", "messageEqualizeDynamic": "Dynamic domain will be equalized with parent", "messageEqualizeStatic": "Static domain will be equalized with parent", "messageChangeDynamic": "Dynamic domain will be changed to <b>{{value}}</b>", "messageChangeStatic": "Static domain will be changed to <b>{{value}}</b>", "messageSwitchEnvironment": "Selected Domain is located on different environment. Please switch environment from \"Entity settings\" page first", "entitySettings": "Entity settings page", "notificationUpdate": "Bulk update completed successfully", "notify": "Please, specify one of these parameters to proceed with search: Name, Server, Domain or press \"Show all\" button", "btnShowAll": "Show all", "btnDownloadCsv": "Download changes", "switchAllConfirm": "All previous changes will be cancelled. Are you sure?"}, "GAME": {"gameNameLabel": "Game Name", "gameCode": "Game Code", "gameUrl": "Game URL", "providerGameCode": "Provider Game Code", "titleCreateGame": "Create Game", "titleEditGame": "Edit Game", "titlePlaceholder": "Enter Game title", "gameCodePlaceholder": "Enter Game code", "providerGameCodePlaceholder": "Enter Provider game code", "urlPlaceholder": "Enter Game URL", "totalBetMultiplier": "Total Bet Multiplier", "totalBetMultiplierPlaceholder": "Enter Total Bet Multiplier", "slot": "Slot", "action": "Action", "table": "Table", "GRID": {"providerTitle": "Game Provider", "title": "Game Name", "code": "Game Code", "status": "Status", "statusActive": "Active", "statusInactive": "Inactive", "category": "Category", "slotCategory": "Slot", "tableCategory": "Table game", "rouletteCategory": "Roulette", "type": "Type", "typeHtml5": "Html5", "typeFlash": "Flash", "typeDownloadable": "Downloadable", "features": "Features", "progressiveFeature": "Progressive", "jackpotFeature": "Jackpot", "brandedFeature": "Branded", "historyRenderType": "Render Type", "limitFiltersWillBeApplied": "Game Limits Filter", "limitFiltersAvailable": "available", "limitFiltersUnavailable": "unavailable"}, "GENERAL": {"name": "Game Name", "description": "Game Description", "defaultInfo": "De<PERSON>ult Info", "historyUrlLabel": "Game history URL", "historyRenderTypeLabel": "History render type", "title": "General", "typeLabel": "Type", "providerLabel": "Provider", "notSet": "Not set", "countriesLabel": "Countries", "schemaDefinitionLabel": "Schema definition"}, "LIMITS": {"title": "Limits", "limitsPlaceholder": "Put limits JSON here", "featuresPlaceholder": "Put features JSON here"}, "FEATURE": {"title": "Features", "featuresPlaceholder": "Put features JSON here", "transferEnabled": "Transfer enabled"}, "SETTINGS": {"title": "Settings", "settingsPlaceholder": "Put settings JSON here"}, "CLIENT_FEATURES": {"title": "Client features", "clientFeaturesPlaceholder": "Put client features JSON here"}}, "MERCHANT": {"PARAMETERS": {"gsId": "Identifies a remote game server within the POP environment", "gpId": "Identifies a casino brand", "supportTransfer": "Support of action games", "supportForceFinishAndRevert": "Support to force finish and revert round", "forceFinishAndRevertInSWWalletOnly": "Support for finishing round in internal wallet", "serverUrl": "Server URL", "password": "Password", "username": "Username", "sameUrlForTerminalLoginAndTicket": "Use one login method for terminal and lobby", "isUnderAAMSRegulation": "Operator is under AAMS regulation", "gameLogoutOptions": {"common": "Game logout options", "type": "Type of games to logout", "maxRetryAttempts": "Attempts to finalize payment", "maxSessionTimeout": "Custom session timeout for the game", "unfinished": "Unfinished", "all": "All"}, "isPromoInternal": "Internal Promotions", "walletPerGame": "Wallet per game", "ignoreBalanceRequestError": "Ignore balance request error", "supportPlayMoney": "Support Play Money"}}, "JURISDICTION": {"title": "Juris<PERSON>", "jurisdictionRemoved": "Jurisdiction was removed", "confirmationRemoved": "Do you really want to delete this jurisdiction?", "notificationAdded": "Jurisdiction was successfully added", "notificationChanged": "Jurisdiction was successfully changed", "GRID": {"title": "Juris<PERSON>", "code": "Code", "description": "Description", "defaultCountry": "Default Country", "allowedCountries": "Allowed Countries", "restrictedCountries": "Restricted Countries", "actionEdit": "Edit", "actionRemove": "Delete"}, "MODAL": {"code": "Code", "title": "Title", "countries": "Countries", "description": "Description", "titleEdit": "Edit jurisdiction", "defaultCountry": "Default Country", "titleCreate": "Create jurisdiction", "allowedCountries": "Allowed Countries", "countriesListType": "Countries List Type", "restrictedCountries": "Restricted Countries"}}, "LABEL_MANAGEMENT": {"title": "Labels Management", "createLabel": "Create Label", "createLabelGroup": "Create Label Group", "confirmationRemoved": "Do you really want to delete \"{{name}}\" label?", "notificationCreated": "Label was created successfully", "notificationGroupCreated": "Label Group was created successfully", "notificationRemoved": "Label was removed", "hintText": "Only labels of the \"Entity\" type can be deleted.", "GRID": {"title": "Title", "group": "Group", "type": "Type", "relationType": "Relation Type", "created": "Created", "updated": "Updated", "delete": "Delete"}, "MODAL": {"title": "Title", "titleCreate": "Create new Label", "titleGroupCreate": "Create new Label Group", "groupType": "Group type", "group": "Group", "groupName": "Group name", "relationType": "Relation Type"}, "TYPES": {"game": "Game", "entity": "Entity", "promotion": "Promotion", "oneToOne": "One to One", "oneToMany": "One to Many"}}, "BI": {"reportNotFound": "Report not found. Please contact Customer Support"}, "GAME_LIMITS": {"addButtonTitle": "Add", "currency": "<PERSON><PERSON><PERSON><PERSON>", "defaultGamePlaceholder": "Default game", "entity": "Entity", "gameGroup": "Game Group", "gameType": "Game Type", "game": "Game", "gameLimitsChangedMessage": "Game limits successfully changed", "gameLimitsNotFoundMessage": "Game limits configuration not found", "readConfigButtonTitle": "Show Custom Limits", "removeButtonTitle": "Remove", "schema": "<PERSON><PERSON><PERSON>", "selectAllButtonTitle": "Select All", "submitButtonTitle": "Submit", "title": "Title", "titlePlaceholder": "Enter title of game limits configuration", "slotLimitsTab": "Slots/Table/Arcade", "liveLimitsTab": "Live Games", "LIVE": {"baseCurrency": "Base Currency", "operatorCurrencies": "Operator Currencies", "expandCollapseAll": "Expand/Collapse All", "createLevel": "Create Level", "addLevel": "Add Level", "selectLevel": "Select Level", "currency": "<PERSON><PERSON><PERSON><PERSON>", "level": "Level", "visibility": "Visibility", "default": "<PERSON><PERSON><PERSON>", "visible": "Visible", "actions": "Actions", "betType": "Bet Type", "payout": "Payout", "min": "Min", "max": "Max", "positionMax": "Position Max", "exposure": "Exposure", "alert": "<PERSON><PERSON>", "block": "Block", "defaultLimitsConfiguration": "Default Limits Configuration", "stakeAll": "Stake All", "stakeDef": "<PERSON><PERSON> Def", "stakeMax": "Stake Max", "stakeMin": "Stake Min", "totalStakeMax": "Total Stake Max", "totalStakeMin": "Total Stake Min", "isDefaultRoom": "Is Room Default", "concurrentPlayers": "Estimated concurrent players", "order": "Order", "customLimits": "Custom Limits", "inheritedLimits": "Inherited Limits", "alignedToEuro": "Aligned to Euro", "updateLimitsMessage": "\"{{currency}}\" limits of \"{{level}}\" level were successfully changed", "changeLevelVisibilityMessage": "Do you really want to change visibility of \"{{level}}\" level?", "levelVisibilityChangedMessage": "Visibility of \"{{level}}\" level was successfully changed", "changeDefaultLevelMessage": "Do you really want to set \"{{level}}\" level as default?", "levelSetAsDefaultMessage": "\"{{level}}\" level was successfully set as default", "resetLimitsMessage": "Do you really want to reset \"{{currency}}\" limits of \"{{level}}\" level to inherited values?", "limitsSuccessfullyReset": "\"{{currency}}\" limits of \"{{level}}\" level were successfully reset to inherited values", "limitsAlreadyInheritedMessage": "\"{{currency}}\" limits of \"{{level}}\" level is already inherited", "deleteLimitsMessage": "Do you really want to delete \"{{level}}\" level", "levelSuccessfullyDeleted": "\"{{level}}\" level was successfully deleted", "duplicateLimitsMessage": "\"{{currency}}\" limits of \"{{level}}\" level were successfully duplicated", "removeLevel": "Do you really want to make \"{{level}}\" level for \"{{currency}}\" invisible for the end user?", "editLimits": "Edit limits", "renameLevelTooltip": "Rename level", "renameLevel": "Rename \"{{title}}\" level", "levelRenamedMessage": "Level was successfully renamed", "resetLimits": "Reset limits to inherited", "deleteLevel": "Delete level", "duplicateLimits": "Duplicate Limits", "createNewLevel": "Create New Level", "addLevelToLimitsConfiguration": "Add An Existing Level To The Limits Configuration", "title": "Title", "levelsSortOrder": "Levels sort order", "descSortOption": "High to low", "ascSortOption": "Low to high", "levelAlreadyExistsInConfiguration": "This level already exists in the limits configuration", "levelAlreadyExists": "This level already exists", "entirelyInheritedMessage": "There is no possibility to remove level from the limits configuration, which is entirely inherited"}}, "LANGUAGES": {"en": "English (EN)", "zh": "中文 (ZH)", "zh-tw": "繁體中文 (ZH-TW)", "ja": "日本語 (JA)", "ms": "Bahasa Malaysia (MS)", "ko": "한국어 (KO)", "th": "ภาษาไทย (TH)", "vi": "<PERSON><PERSON><PERSON><PERSON> (VI)", "id": "Bahasa Indonesia (ID)", "ro": "Român<PERSON> (RO)", "it": "<PERSON><PERSON> (IT)", "el": "Ελληνικά (EL)", "km": "កម្ពុជា (KM)", "es": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ES)", "pt": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PT)", "pt-br": "<PERSON>ug<PERSON><PERSON><PERSON> (PT-BR)", "ru": "Русский (RU)", "de": "<PERSON><PERSON><PERSON> (DE)", "sv": "Svenska (SV)", "da": "Dansk (DA)", "nl": "Nederlands (NL)", "bg": "Български (BG)", "sr": "<PERSON><PERSON><PERSON> (SR)", "tr": "Türkçe (TR)", "ENGLISH": {"en": "English (EN)", "th": "Thai (TH)", "vi": "Vietnamese (VI)", "id": "Bahasa Indonesian (ID)", "zh-cn": "Simplified Chinese (ZH-CN)", "ko": "Korean (KO)", "ro": "Romanian (RO)", "it": "Italian (IT)", "es": "Spanish (ES)", "el": "Greek (EL)", "km": "Cambodian (KM)", "pt": "Portuguese (PT)", "pt-br": "Brazilian Portuguese (PT-BR)", "ru": "Russian (RU)", "de": "German (DE)", "sv": "Swedish (SV)", "da": "Danish (DA)", "nl": "Dutch (NL)", "bg": "Bulgarian (BG)", "sr": "Serbian (SR)", "tr": "Turkish (TR)"}}, "TITLE": "Skywind Back-office / Casino", "HUBS": {"analytics": "Analytics", "casino": "Casino", "engagement": "Engagement", "studio": "Live Studio"}, "BI-REPORTS-SWITCH": {"pid": "PID", "baseUrl": "Base Url", "trustServerUrl": "Trust Server Url", "successSaveMessage": "BI reports {{pair}} was saved successfully"}}